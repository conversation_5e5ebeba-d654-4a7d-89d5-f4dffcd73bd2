## BSL

Base software layer.

## Welcome to BSL

Here's welcome news.

## Building

Go to the project directory and run compilation script.

```
sh build.sh
```

This will compiled whole project files and output target to directory `target/install`.

After build it will generate a file `gcc-toolchain.conf`, it indicate the path for compile tool chain.

## Convention

**Preamble format of the source file.**

```
/*******************************************************************************
* ZHT Communication & technology Co.Ltd 2019-2029 Copyright reserved
* File : example.c
* Function : This is an example for the format of source file.
* Author : xxx
* Created on: 2019.xx.xx
*
* Modification history:
* Ver Who Date Changes
* ------------------------------------------------------------------------------
* V1.0 xxx xxxx/xx/xx First release
********************************************************************************/
```
