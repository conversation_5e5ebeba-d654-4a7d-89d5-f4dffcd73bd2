
cmake_minimum_required(VERSION 3.0)

project(BSL)

set(TARGET_NAME bsl)
set(TARGET_STATIC_NAME bsl-static)

set(TARGET_FULL_VERSION 1.0.1)
set(TARGET_MAIN_VERSION 1)

set(CMAKE_C_FLAGS ${CMAKE_C_FLAGS} "-std=c99 -Wall -D_GNU_SOURCE -Wno-unused-function")
set(CMAKE_CXX_FLAGS ${CMAKE_CXX_FLAGS} "-std=c99 -Wall -D_GNU_SOURCE -Wno-unused-function")
message(STATUS "CMAKE_C_FLAGS = ${CMAKE_C_FLAGS}")
message(STATUS "CMAKE_CXX_FLAGS = ${CMAKE_CXX_FLAGS}")

set(SRC_SON_DIRS "${PROJECT_SOURCE_DIR}/src")

file(GLOB CHILD_DIRS RELATIVE "${SRC_SON_DIRS}" "${SRC_SON_DIRS}/*")

foreach(DIR ${CHILD_DIRS})
    if(IS_DIRECTORY "${SRC_SON_DIRS}/${DIR}")
        # Check if there is same header file
        if(EXISTS "${SRC_SON_DIRS}/${DIR}/${DIR}.h")
            aux_source_directory(${SRC_SON_DIRS}/${DIR} SRC_LIST)
        else()
            file(GLOB SUB_DIRS RELATIVE "${SRC_SON_DIRS}/${DIR}" "${SRC_SON_DIRS}/${DIR}/*")
            foreach(SUB_DIR ${SUB_DIRS})
                if(IS_DIRECTORY "${SRC_SON_DIRS}/${DIR}/${SUB_DIR}")
                    if(EXISTS "${SRC_SON_DIRS}/${DIR}/${SUB_DIR}/${SUB_DIR}.h")
                        aux_source_directory(${SRC_SON_DIRS}/${DIR}/${SUB_DIR} SRC_LIST)
                    endif()
                endif()
            endforeach()
        endif()
    endif()
endforeach()

include_directories(${PROJECT_SOURCE_DIR}/target/install/include)
include_directories(${PROJECT_SOURCE_DIR}/src/bLog)

link_directories(${PROJECT_SOURCE_DIR}/target/install/lib)

add_library(${TARGET_NAME} SHARED ${SRC_LIST})
add_library(${TARGET_STATIC_NAME} STATIC ${SRC_LIST})

set_target_properties(${TARGET_NAME} PROPERTIES
    VERSION ${TARGET_FULL_VERSION}
    SOVERSION ${TARGET_MAIN_VERSION}
)

set_target_properties(${TARGET_STATIC_NAME} PROPERTIES OUTPUT_NAME ${TARGET_NAME})

target_link_libraries(${TARGET_NAME} pthread cjson zlog)
target_link_libraries(${TARGET_STATIC_NAME} pthread cjson zlog)

install(TARGETS ${TARGET_NAME}
    LIBRARY DESTINATION ${PROJECT_SOURCE_DIR}/target/install/lib
    ARCHIVE DESTINATION ${PROJECT_SOURCE_DIR}/target/install/lib
    RUNTIME DESTINATION ${PROJECT_SOURCE_DIR}/target/install/bin
)

install(TARGETS ${TARGET_STATIC_NAME}
    LIBRARY DESTINATION ${PROJECT_SOURCE_DIR}/target/install/lib
    ARCHIVE DESTINATION ${PROJECT_SOURCE_DIR}/target/install/lib
    RUNTIME DESTINATION ${PROJECT_SOURCE_DIR}/target/install/bin
)

foreach(DIR ${CHILD_DIRS})
    if(IS_DIRECTORY "${SRC_SON_DIRS}/${DIR}")
        # Check if there is same header file
        if(EXISTS "${SRC_SON_DIRS}/${DIR}/${DIR}.h")
            install(FILES ${SRC_SON_DIRS}/${DIR}/${DIR}.h DESTINATION ${PROJECT_SOURCE_DIR}/target/install/include)
        else()
            file(GLOB SUB_DIRS RELATIVE "${SRC_SON_DIRS}/${DIR}" "${SRC_SON_DIRS}/${DIR}/*")
            foreach(SUB_DIR ${SUB_DIRS})
                if(IS_DIRECTORY "${SRC_SON_DIRS}/${DIR}/${SUB_DIR}")
                    if(EXISTS "${SRC_SON_DIRS}/${DIR}/${SUB_DIR}/${SUB_DIR}.h")
                        install(FILES ${SRC_SON_DIRS}/${DIR}/${SUB_DIR}/${SUB_DIR}.h DESTINATION ${PROJECT_SOURCE_DIR}/target/install/include)
                    endif()
                endif()
            endforeach()
        endif()
    endif()
endforeach()
