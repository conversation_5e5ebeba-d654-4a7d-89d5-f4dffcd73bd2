/*******************************************************************************
* ZHT Communication & technology Co.Ltd 2019-2029 Copyright reserved
* File : bTimer.h
* Function : Base on linux timerfd, a useful timer lib
* Author : yangzuogui
* Created on: 2025.07.07
*
* Modification history:
* Ver Who Date Changes
* ------------------------------------------------------------------------------
* V1.0 yangzuogui 2025/07/04 First release
********************************************************************************/
#include <stdio.h>
#include <errno.h>
#include <stdlib.h>
#include <stdbool.h>
#include <string.h>
#include <unistd.h>
#include <pthread.h>
#include <sys/epoll.h>
#include <sys/timerfd.h>
#include <sys/eventfd.h>
#include <pthread.h>
#if defined(__linux__)
#include <sys/prctl.h>
#endif
#include "uthash.h"
#include "bTimer.h"

// #define _B_TIMER_DEBUG
#ifdef _B_TIMER_DEBUG
#define     BTIMER_DEBUG(...)          fprintf(stderr, "[BTIMER] "__VA_ARGS__);
#define     BTIMER_INFO(...)           fprintf(stderr, "[BTIMER] "__VA_ARGS__);
#define     BTIMER_ERROR(...)          fprintf(stderr, "[BTIMER] "__VA_ARGS__);
#else
#define     BTIMER_DEBUG(...)
#define     BTIMER_INFO(...) 
#define     BTIMER_ERROR(...)
#endif


typedef struct timerFlag {
    bool threadActive;          // 线程是否已创建
    bool threadRunning;         // 线程当前是否在执行任务
    bool threadExit;            // 线程退出标志
    bool hasPendingTask;        // 是否有待处理任务
    bool deletePending;         // 标记待删除状态
}timerFlag_t;

typedef struct fdTimer {
    int32_t timerFd;
    int32_t timerCnt;
    void *timerData;
    UT_hash_handle hh;
    timerCallbackFunc timerCallback;
    timerReleaseFunc releaseCallback;
    // 线程管理相关字段
    pthread_t callbackThread;
    pthread_mutex_t threadMutex;
    pthread_cond_t threadCond;
    timerFlag_t flag;
    // 高精度时间跟踪
    uint64_t lastTriggerNs;         // 上次触发时间(纳秒)
    uint64_t intervalNs;            // 间隔时间(纳秒)
    uint64_t nextTriggerNs;         // 下次触发时间(纳秒)
}fdTimer_t;


struct bTimer {
    int32_t timerMaxEvent;          // epoll 最大事件数
    int32_t currentTasks;           // 当前任务数
    int32_t timerEpollFd;           // epoll 管理句柄
    int32_t wakeupFd;               // 用于唤醒 epoll_wait 的文件描述符
    bool timerActiveFlag;
    fdTimer_t *timerHead;
    pthread_t timerThreadId;
    pthread_rwlock_t timerRwLock;
};

/**
 * @brief  Get monotonic time (milliseconds)
 * @param  none
 * @return return ms;
 */
static uint64_t getMonotonicTimeMs(void)
{
    struct timespec ts;
    clock_gettime(CLOCK_MONOTONIC, &ts);
    return (uint64_t)ts.tv_sec * 1000ULL + (uint64_t)ts.tv_nsec / 1000000ULL;
}

/**
 * @brief  Get monotonic time (nanoseconds)
 * @param  none
 * @return return ns;
 */
static uint64_t getMonotonicTimeNs(void)
{
    struct timespec ts;
    clock_gettime(CLOCK_MONOTONIC, &ts);
    return (uint64_t)ts.tv_sec * 1000000000ULL + (uint64_t)ts.tv_nsec;
}


/**
 * @brief  Timer local entity thread
 * @param  arg: a bTimer_t pointer for timer;
 * @return always return null;
 */
static void *timerThread(void *arg)
{
    bTimer_t *ptTimer = (bTimer_t *)arg;
    struct epoll_event events[ptTimer->timerMaxEvent];

    while(ptTimer->timerActiveFlag) {
        /* 1. epoll_wait for timerfd event */
        int32_t nfds = epoll_wait(ptTimer->timerEpollFd, events, ptTimer->timerMaxEvent, -1);
        if(nfds <= 0){
            if (errno == EINTR) {
                continue;
            }
            BTIMER_ERROR("epoll_wait error: %s\n", strerror(errno));
            break;
        }
        int32_t i = 0;
        for(i = 0; i < nfds; i++){
            // 2. Check if exit event?
            if (events[i].data.ptr == (void*)(intptr_t)(-1)) {
                // read for accept event
                uint64_t u;
                ssize_t s = read(ptTimer->wakeupFd, &u, sizeof(uint64_t));
                (void)s;
                BTIMER_DEBUG("Wakeup signal received\n");
                continue;
            }

            // 2. normal event, read for clear event
            fdTimer_t *ptFdTimer = (fdTimer_t *)events[i].data.ptr;
            /* unknow pointer */
            if(NULL == ptFdTimer){
                continue;
            }
            /* read for clear event */
            char buf[128] = {0};
            while(read(ptFdTimer->timerFd, buf, sizeof(buf)) > 0);
            /* if null for callback, ignore it */
            if(NULL == ptFdTimer->timerCallback){
                continue;
            }

            // record time
            uint64_t triggerTimeNs = getMonotonicTimeNs();
            ptFdTimer->lastTriggerNs = triggerTimeNs;
            ptFdTimer->nextTriggerNs = triggerTimeNs + ptFdTimer->intervalNs;

            pthread_mutex_lock(&ptFdTimer->threadMutex);
            // If the thread is processing the previous task, it is marked as having a pending task
            if(ptFdTimer->flag.threadRunning) {
                ptFdTimer->flag.hasPendingTask = true;
                BTIMER_DEBUG("Timer %d has pending task\n", ptFdTimer->timerFd);
            } else {
                // Otherwise, wake up the thread and process it immediately
                ptFdTimer->flag.hasPendingTask = true;
                pthread_cond_signal(&ptFdTimer->threadCond);
            }
            pthread_mutex_unlock(&ptFdTimer->threadMutex);

            if(ptFdTimer->timerCnt > 0){
                ptFdTimer->timerCnt--;
                if(ptFdTimer->timerCnt == 0){
                    BTIMER_DEBUG("Timer %d reached zero count, marking for deletion\n", ptFdTimer->timerFd);
                    // 1. Remove the timer from epoll
                    struct epoll_event event;
                    event.events = EPOLLIN | EPOLLET;
                    event.data.ptr = (void *)ptFdTimer;
                    epoll_ctl(ptTimer->timerEpollFd, EPOLL_CTL_DEL, ptFdTimer->timerFd, &event);

                    // 2. Mark for deletion
                    pthread_mutex_lock(&ptFdTimer->threadMutex);
                    ptFdTimer->flag.deletePending = true;
                    pthread_mutex_unlock(&ptFdTimer->threadMutex);

                    // 3. Wake up the thread to process the last possible pending task
                    pthread_mutex_lock(&ptFdTimer->threadMutex);
                    pthread_cond_signal(&ptFdTimer->threadCond);
                    pthread_mutex_unlock(&ptFdTimer->threadMutex);

                    // 4. Remove from hash table
                    pthread_rwlock_wrlock(&ptTimer->timerRwLock);
                    HASH_DEL(ptTimer->timerHead, ptFdTimer);
                    ptTimer->currentTasks--;
                    pthread_rwlock_unlock(&ptTimer->timerRwLock);

                    // 5. Set thread exit flag
                    pthread_mutex_lock(&ptFdTimer->threadMutex);
                    ptFdTimer->flag.threadExit = true;
                    pthread_cond_signal(&ptFdTimer->threadCond);
                    pthread_mutex_unlock(&ptFdTimer->threadMutex);
                }
            }
        }
    }
    pthread_exit(NULL);
}

/**
 * @brief  Timer Alloc Momory function
 * @param  void, no args need;
 * @return return bTimer_t pointer if successful, Null will be return is failed;
 */
static bTimer_t *timerAlloc(void)
{
    bTimer_t *ptNewTimer = malloc(sizeof(bTimer_t));
    if(ptNewTimer){
        memset(ptNewTimer, 0, sizeof(bTimer_t));
        ptNewTimer->timerHead = NULL;
        ptNewTimer->timerActiveFlag = false;
        ptNewTimer->timerEpollFd = -1;
        ptNewTimer->timerMaxEvent = 0;
        ptNewTimer->currentTasks  = 0;
        ptNewTimer->timerThreadId = 0;
        pthread_rwlock_init(&ptNewTimer->timerRwLock, NULL);
    }
    return ptNewTimer;
}

/**
 * @brief  Timer callback thread entity
 * @param  arg: a fdTimer_t pointer for each real timer;
 * @return always return NULL;
 */
static void* timerCallbackThread(void *arg) 
{
    fdTimer_t *ptFdTimer = (fdTimer_t *)arg;

    pthread_mutex_lock(&ptFdTimer->threadMutex);
    while(!ptFdTimer->flag.threadExit) {
        // 1. Wait Task
        while(!ptFdTimer->flag.hasPendingTask && !ptFdTimer->flag.threadExit) {
            pthread_cond_wait(&ptFdTimer->threadCond, &ptFdTimer->threadMutex);
        }

        // 2. Check exit?
        if(ptFdTimer->flag.threadExit){
            break;
        }

        // 3. make flag to mark busy status
        ptFdTimer->flag.threadRunning = true;
        ptFdTimer->flag.hasPendingTask = false;
        pthread_mutex_unlock(&ptFdTimer->threadMutex);

        // 4. Do real callback
        if(ptFdTimer->timerCallback) {
            ptFdTimer->timerCallback(ptFdTimer->timerData);
        }

        // 5. When Task callback return, make flag to idle status
        pthread_mutex_lock(&ptFdTimer->threadMutex);
        ptFdTimer->flag.threadRunning = false;

        // 6. Check if need to exit?
        if(ptFdTimer->flag.deletePending && !ptFdTimer->flag.hasPendingTask) {
            ptFdTimer->flag.threadExit = true;
            BTIMER_DEBUG("Callback thread for timer %d finishing last task\n", ptFdTimer->timerFd);
        }
    }
    pthread_mutex_unlock(&ptFdTimer->threadMutex);

    // 7. Thread exit, release some resource
    BTIMER_DEBUG("Callback thread for timer %d exiting\n", ptFdTimer->timerFd);
    if(ptFdTimer->releaseCallback) {
        ptFdTimer->releaseCallback(ptFdTimer->timerData);
    }

    // 8. close fd and free memory
    if(ptFdTimer->timerFd >= 0) {
        close(ptFdTimer->timerFd);
        ptFdTimer->timerFd = -1;
    }
    pthread_mutex_destroy(&ptFdTimer->threadMutex);
    pthread_cond_destroy(&ptFdTimer->threadCond);
    free(ptFdTimer);
    return NULL;
}


/**
 * @brief  Create a timer handler
 * @param  maxNum: Max task num to the Timer support;
 * @return return a not-null pointer as bTimer_t if successful, else will return NULL if failed
 */
bTimer_t *bTimerCreate(int maxNum)
{
    if(maxNum <= 0){
        return NULL;
    }

    bTimer_t *timer = timerAlloc();
    if(timer == NULL){
        return NULL;
    }
    // 1. Create a  eventfd to wakeup epoll when exit
    timer->wakeupFd = eventfd(0, EFD_NONBLOCK);
    if (timer->wakeupFd < 0) {
        BTIMER_ERROR("Failed to create wakeup eventfd: %s\n", strerror(errno));
        free(timer);
        return NULL;
    }

    // 2. Create epoll
    timer->timerMaxEvent = maxNum;
    timer->timerActiveFlag = true;
    timer->timerEpollFd = epoll_create(maxNum);
    if(timer->timerEpollFd < 0){
        close(timer->wakeupFd);
        free(timer);
        return NULL;
    }
    
    // 2. Add wakeupFd to epoll
    struct epoll_event event;
    event.events = EPOLLIN;
    event.data.ptr = (void*)(intptr_t)(-1);  // Special value for exit
    if (epoll_ctl(timer->timerEpollFd, EPOLL_CTL_ADD, timer->wakeupFd, &event) < 0) {
        BTIMER_ERROR("Failed to add wakeup fd to epoll: %s\n", strerror(errno));
        close(timer->wakeupFd);
        close(timer->timerEpollFd);
        free(timer);
        return NULL;
    }

    if(pthread_create(&timer->timerThreadId, NULL, timerThread, (void *)timer) != 0){
        close(timer->wakeupFd);
        close(timer->timerEpollFd);
        free(timer);
        return NULL;
    }

    return timer;
}


/**
 * @brief  Add a task to timer
 * @param  ptTimer: a timer handler pointer;
 * @param  intervalMs: timer interval in ms;
 * @param  repeat: repeat count of timer, if -1 is set, will be forever, otherwise, is Limited times to repeat;
 * @param  cb: Timing callback function pointer;
 * @param  rb: The callback function triggered when the repetition count is reach to zero;
 * @return return 0 is success, or negative number if an error occurred(in which case, errno is set appropriately);
 */
int bTimerAddTask(bTimer_t *ptTimer, uint32_t intervalMs, int repeat, timerCallbackFunc cb, void *data, timerReleaseFunc rb)
{
    if(ptTimer == NULL){
        return -1;
    }
    // Check if release
    if(ptTimer->timerActiveFlag == false){
        return -1;
    }

    // Check Max Task Limite
    pthread_rwlock_wrlock(&ptTimer->timerRwLock);
    if (ptTimer->currentTasks >= ptTimer->timerMaxEvent) {
        pthread_rwlock_unlock(&ptTimer->timerRwLock);
        BTIMER_ERROR("Cannot add task: maximum tasks (%d) reached\n", ptTimer->timerMaxEvent);
        return -5;
    }

    fdTimer_t *ptFdTimer = NULL;
    // 1. Alloc Memory
    ptFdTimer = (fdTimer_t *)malloc(sizeof(fdTimer_t));
    if(NULL == ptFdTimer){
        pthread_rwlock_unlock(&ptTimer->timerRwLock);
        return -3;
    }

    // 2. Init timerFd
    memset(ptFdTimer, 0, sizeof(fdTimer_t));
    ptFdTimer->timerCallback    = cb;
    ptFdTimer->releaseCallback  = rb;
    ptFdTimer->timerCnt         = repeat;
    ptFdTimer->timerData        = data;
    ptFdTimer->timerFd          = timerfd_create(CLOCK_MONOTONIC, TFD_CLOEXEC|TFD_NONBLOCK);
    if(ptFdTimer->timerFd < 0){
        free(ptFdTimer);
        pthread_rwlock_unlock(&ptTimer->timerRwLock);
        return -1;
    }

    // 3. Set timing
    struct itimerspec itimespec;
    uint32_t seconds = intervalMs / 1000;
    uint32_t us = (intervalMs % 1000) * 1000 * 1000;
    itimespec.it_value.tv_sec       = seconds;
    itimespec.it_value.tv_nsec      = us;
    itimespec.it_interval.tv_sec    = seconds;
    itimespec.it_interval.tv_nsec   = us;
    if(timerfd_settime(ptFdTimer->timerFd, 0, &itimespec, NULL) == -1){
        close(ptFdTimer->timerFd);
        free(ptFdTimer);
        pthread_rwlock_unlock(&ptTimer->timerRwLock);
        return -2;
    }

    // 4. Add to hash
    HASH_ADD_INT(ptTimer->timerHead, timerFd, ptFdTimer);
    ptTimer->currentTasks++;
    pthread_rwlock_unlock(&ptTimer->timerRwLock);

    // 5. Init flags
    pthread_mutex_init(&ptFdTimer->threadMutex, NULL);
    pthread_cond_init(&ptFdTimer->threadCond, NULL);
    ptFdTimer->flag.threadActive = false;
    ptFdTimer->flag.threadRunning = false;
    ptFdTimer->flag.threadExit = false;
    ptFdTimer->flag.hasPendingTask = false;

    // 6. Run Threads
    if(pthread_create(&ptFdTimer->callbackThread, NULL, timerCallbackThread, (void*)ptFdTimer) != 0) {
        BTIMER_ERROR("Failed to create callback thread for timer %d\n", ptFdTimer->timerFd);
        pthread_mutex_destroy(&ptFdTimer->threadMutex);
        pthread_cond_destroy(&ptFdTimer->threadCond);

        pthread_rwlock_wrlock(&ptTimer->timerRwLock);
        HASH_DEL(ptTimer->timerHead, ptFdTimer);
        ptTimer->currentTasks--;
        pthread_rwlock_unlock(&ptTimer->timerRwLock);

        close(ptFdTimer->timerFd);
        free(ptFdTimer);
        return -5;
    }

    // 7. Set Active flag
    ptFdTimer->flag.threadActive = true;
    BTIMER_DEBUG("Created callback thread for timer %d\n", ptFdTimer->timerFd);

    // 8. Add to epoll
    struct epoll_event event;
    event.events = EPOLLIN | EPOLLET;
    event.data.ptr = ptFdTimer;
    if(epoll_ctl(ptTimer->timerEpollFd, EPOLL_CTL_ADD, ptFdTimer->timerFd, &event) < 0){
        pthread_mutex_destroy(&ptFdTimer->threadMutex);
        pthread_cond_destroy(&ptFdTimer->threadCond);

        pthread_rwlock_wrlock(&ptTimer->timerRwLock);
        HASH_DEL(ptTimer->timerHead, ptFdTimer);
        ptTimer->currentTasks--;
        pthread_rwlock_unlock(&ptTimer->timerRwLock);

        close(ptFdTimer->timerFd);
        free(ptFdTimer);
        return -4;
    }
    // init time
    uint64_t nowNs = getMonotonicTimeNs();
    ptFdTimer->intervalNs = (uint64_t)intervalMs * 1000000ULL;
    ptFdTimer->nextTriggerNs = nowNs + ptFdTimer->intervalNs;
    ptFdTimer->lastTriggerNs = 0;

    return ptFdTimer->timerFd;
}


/**
 * @brief  Detele a task from timer
 * @param  ptTimer: a timer handler pointer;
 * @param  timerfd: task fd which return by bTimerAddTask
 * @return return 0 is success, or -1 if an error occurred(in which case, errno is set appropriately);
 */
int bTimerDeleteTask(bTimer_t *ptTimer, int timerfd)
{
    if(ptTimer == NULL){
        return -1;
    }
    // Check if release
    if(ptTimer->timerActiveFlag == false){
        return -1;
    }

    fdTimer_t *ptFdTimer = NULL;
    // 1. Find fdTimer by timerfd
    pthread_rwlock_rdlock(&ptTimer->timerRwLock);
    HASH_FIND_INT(ptTimer->timerHead, &timerfd, ptFdTimer);
    pthread_rwlock_unlock(&ptTimer->timerRwLock);
    if(NULL == ptFdTimer){
        return -1;
    }

    // 2. Delete it from epoll
    struct epoll_event event;
    event.data.ptr = (void *)ptFdTimer;
    event.events = EPOLLIN | EPOLLET;
    if(epoll_ctl(ptTimer->timerEpollFd, EPOLL_CTL_DEL, ptFdTimer->timerFd, &event) < 0){
        return -1;
    }

    // 3. Delete it from hash
    pthread_rwlock_wrlock(&ptTimer->timerRwLock);
    HASH_DEL(ptTimer->timerHead, ptFdTimer);
    ptTimer->currentTasks--;
    pthread_rwlock_unlock(&ptTimer->timerRwLock);

    // 4. Set some flag to make thread exit
    pthread_mutex_lock(&ptFdTimer->threadMutex);
    ptFdTimer->flag.deletePending = true;
    ptFdTimer->flag.threadExit = true;

    // 5. Clear all panding task
    if(ptFdTimer->flag.hasPendingTask && !ptFdTimer->flag.threadRunning) {
        ptFdTimer->flag.hasPendingTask = false;
    }

    // 6. Make signal to thread(callbackThread)
    pthread_cond_signal(&ptFdTimer->threadCond);
    pthread_mutex_unlock(&ptFdTimer->threadMutex);

    // 7. Wait for thread exit, use timeout wait for 2 seconds
    struct timespec ts;
    clock_gettime(CLOCK_REALTIME, &ts);
    ts.tv_sec += 2;
    int result = pthread_timedjoin_np(ptFdTimer->callbackThread, NULL, &ts);
    if(result != 0) {
        if(result == ETIMEDOUT) {
            BTIMER_ERROR("Thread for timer %d didn't exit in time, detaching\n", timerfd);
            pthread_detach(ptFdTimer->callbackThread);
        } else {
            BTIMER_ERROR("Failed to join thread for timer %d: %s\n",  timerfd, strerror(result));
        }
    }
    return 0;
}


/**
 * @brief  Timer Release function, Release some resource;
 * @param  ptTimer: a timer handler pointer;
 * @return no return value;
 */
void bTimerRelease(bTimer_t *ptTimer)
{
    if(ptTimer){
        // 1. Set thread Exit flag.
        ptTimer->timerActiveFlag = false;

        // 2. wakeup epoll_wait
        uint64_t u = 1;
        if (write(ptTimer->wakeupFd, &u, sizeof(uint64_t)) != sizeof(uint64_t)) {
            BTIMER_ERROR("Failed to write wakeup signal: %s\n", strerror(errno));
        }

        // 3. Wait for thread exit
        pthread_join(ptTimer->timerThreadId, NULL);
        BTIMER_INFO("Timer timerThreadId exit\n");

        // 4. Delete all task from timer
        fdTimer_t *current, *tmp;
        pthread_rwlock_wrlock(&ptTimer->timerRwLock);
        HASH_ITER(hh, ptTimer->timerHead, current, tmp) {
            // 4.1 Detele hash
            HASH_DEL(ptTimer->timerHead, current);
            ptTimer->currentTasks--;

            // 4.2 Set some flag to make thread exit
            pthread_mutex_lock(&current->threadMutex);
            current->flag.deletePending = true;
            current->flag.threadExit = true;
            BTIMER_INFO("Timer set threadExit\n");

            // 4.3 Clear all panding task
            if(current->flag.hasPendingTask && !current->flag.threadRunning) {
                current->flag.hasPendingTask = false;
            }

            // 4.4 Make signal to thread(callbackThread)
            pthread_cond_signal(&current->threadCond);
            pthread_mutex_unlock(&current->threadMutex);

            // 4.5 Wait for thread exit, use timeout wait for 2 seconds
            struct timespec ts;
            clock_gettime(CLOCK_REALTIME, &ts);
            ts.tv_sec += 2;
            int result = pthread_timedjoin_np(current->callbackThread, NULL, &ts);
            if(result != 0) {
                if(result == ETIMEDOUT) {
                    BTIMER_ERROR("Thread for timer %d didn't exit in time, detaching\n", current->timerFd);
                    pthread_detach(current->callbackThread);
                } else {
                    BTIMER_ERROR("Failed to join thread for timer %d: %s\n", current->timerFd, strerror(result));
                }
            }

            // 4.6 Clean up fdTimer resources and free memory
            if(current->timerFd >= 0) {
                close(current->timerFd);
                current->timerFd = -1;
            }
            pthread_mutex_destroy(&current->threadMutex);
            pthread_cond_destroy(&current->threadCond);
            free(current);
            BTIMER_DEBUG("Released fdTimer %p and its resources\n", current);
        }
        pthread_rwlock_unlock(&ptTimer->timerRwLock);

        // 5. Close fd and free memory
        close(ptTimer->wakeupFd);
        close(ptTimer->timerEpollFd);
        pthread_rwlock_destroy(&ptTimer->timerRwLock);
        free(ptTimer);
        BTIMER_INFO("Timer manager released successfully\n");
    }
}

/**
 * @brief  Get Task remain repeat count
 * @param  ptTimer: a timer handler pointer;
 * @param  timerfd: a task timerfd handler;
 * @param  remainingCnt: a uint64_t pointer to Remaining Trigger Count
 * @return if failed -1 is return, else 0 for successful 
 */
int bTimerGetRemainingCount(bTimer_t *ptTimer, int timerfd, int *remainingCnt)
{
    if(ptTimer && ptTimer->timerActiveFlag){
        fdTimer_t *ptFdTimer = NULL;
        pthread_rwlock_rdlock(&ptTimer->timerRwLock);
        HASH_FIND_INT(ptTimer->timerHead, &timerfd, ptFdTimer);
        pthread_rwlock_unlock(&ptTimer->timerRwLock);
        if(NULL == ptFdTimer){
            return -1;
        }
        *remainingCnt = ptFdTimer->timerCnt;
        return 0;
    }else{
        return -1;
    }
    return -1;
}

/**
 * @brief  Get Task remain time in ms
 * @param  ptTimer: a timer handler pointer;
 * @param  timerfd: a task timerfd handler;
 * @param  remainingMs: a uint64_t pointer to Remaining time (milliseconds) output parameter;
 * @return return zero if successful, return -1 if failed
 */
int bTimerGetRemainingTime(bTimer_t *ptTimer, int timerfd, uint64_t *remainingMs) 
{
    if (!ptTimer || !remainingMs) {
        BTIMER_ERROR("Invalid arguments\n");
        return -1;
    }
    if(ptTimer->timerActiveFlag == false){
        return -1;
    }

    fdTimer_t *ptFdTimer = NULL;
    pthread_rwlock_rdlock(&ptTimer->timerRwLock);
    HASH_FIND_INT(ptTimer->timerHead, &timerfd, ptFdTimer);
    pthread_rwlock_unlock(&ptTimer->timerRwLock);
    if (ptFdTimer == NULL) {
        BTIMER_DEBUG("Timer %d not found\n", timerfd);
        return -1;
    }

    uint64_t nowNs = getMonotonicTimeNs();
    if (nowNs >= ptFdTimer->nextTriggerNs) {
        *remainingMs = 0;
    } else {
        *remainingMs = (ptFdTimer->nextTriggerNs - nowNs + 500000) / 1000000ULL;
    }
    return 0;
}

/**
 * @brief Get the timestamp (in milliseconds) of the next time the timer is triggered
 * @param ptTimer Timer Manager Pointer
 * @param timerfd Scheduled task handle
 * @param nextTrigger Next trigger timestamp (milliseconds) output parameter
 * @return 0:成功 -1:失败
 */
int bTimerGetNextTriggerTime(bTimer_t *ptTimer, int timerfd, uint64_t *nextTrigger) 
{
    if (!ptTimer || !nextTrigger) {
        BTIMER_ERROR("Invalid arguments\n");
        return -1;
    }
    if(ptTimer->timerActiveFlag == false){
        return -1;
    }

    fdTimer_t *ptFdTimer = NULL;
    pthread_rwlock_rdlock(&ptTimer->timerRwLock);
    HASH_FIND_INT(ptTimer->timerHead, &timerfd, ptFdTimer);
    pthread_rwlock_unlock(&ptTimer->timerRwLock);
    if (ptFdTimer == NULL) {
        BTIMER_DEBUG("Timer %d not found\n", timerfd);
        return -1;
    }

    // Get Next time
    uint64_t nowNs = getMonotonicTimeNs();
    *nextTrigger = (ptFdTimer->nextTriggerNs - nowNs + 500000) / 1000000ULL;

    return 0;
}

/**
 * @brief Get the interval (in milliseconds) of task
 * @param ptTimer Timer Manager Pointer
 * @param timerfd Scheduled task handle
 * @param intervalMs interval timestamp (milliseconds) output parameter
 * @return 0:success -1:fail
 */
int bTimerGetInterval(bTimer_t *ptTimer, int timerfd, uint64_t *intervalMs) 
{
    if (!ptTimer || !intervalMs) {
        BTIMER_ERROR("Invalid arguments\n");
        return -1;
    }
    if(ptTimer->timerActiveFlag == false){
        return -1;
    }

    fdTimer_t *ptFdTimer = NULL;
    pthread_rwlock_rdlock(&ptTimer->timerRwLock);
    HASH_FIND_INT(ptTimer->timerHead, &timerfd, ptFdTimer);
    pthread_rwlock_unlock(&ptTimer->timerRwLock);
    if (ptFdTimer == NULL) {
        BTIMER_DEBUG("Timer %d not found\n", timerfd);
        return -1;
    }

    struct itimerspec currValue;
    if (timerfd_gettime(ptFdTimer->timerFd, &currValue)) {
        return -1;
    }

    // Convert to milliseconds
    uint64_t ms = currValue.it_interval.tv_sec * 1000ULL;
    ms += currValue.it_interval.tv_nsec / 1000000ULL;

    *intervalMs = ms;
    return 0;
}

/**
 * @brief  Get Timer task count
 * @param  ptTimer: a timer handler pointer;
 * @return return a value of task count;
 */
int bTimerGetTaskCnt(bTimer_t *ptTimer)
{
    if (!ptTimer) {
        BTIMER_ERROR("Invalid arguments\n");
        return -1;
    }
    if(ptTimer->timerActiveFlag == false){
        return -1;
    }
    return HASH_COUNT(ptTimer->timerHead);
}
