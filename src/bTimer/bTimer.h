/*******************************************************************************
* ZHT Communication & technology Co.Ltd 2019-2029 Copyright reserved
* File : bTimer.h
* Function : Base on linux timerfd, a useful timer lib
* Author : yangzuogui
* Created on: 2025.07.07
*
* Modification history:
* Ver Who Date Changes
* ------------------------------------------------------------------------------
* V1.0 yangzuogui 2025/07/04 First release
********************************************************************************/
#ifndef __B_TIMER_H__
#define __B_TIMER_H__

#ifdef __cplusplus
extern "C" {
#endif

#include <stdint.h>
typedef void (*timerReleaseFunc)(void *data);
typedef void (*timerCallbackFunc)(void *data);
typedef struct bTimer bTimer_t;



/**
 * @brief  Create a timer handler
 * @param  maxNum: Max task num to the Timer support;
 * @return return a not-null pointer as bTimer_t if successful, else will return NULL if failed
 */
bTimer_t *bTimerCreate(int maxNum);

/**
 * @brief  Add a task to timer
 * @param  ptTimer: a timer handler pointer;
 * @param  intervalMs: timer interval in ms;
 * @param  repeat: repeat count of timer, if -1 is set, will be forever, otherwise, is Limited times to repeat;
 * @param  cb: Timing callback function pointer;
 * @param  rb: The callback function triggered when the repetition count is reach to zero;
 * @return return 0 is success, or negative number if an error occurred(in which case, errno is set appropriately);
 */
int bTimerAddTask(bTimer_t *ptTimer, uint32_t intervalMs, int repeat, timerCallbackFunc cb, void *data, timerReleaseFunc rb);

/**
 * @brief  Detele a task from timer
 * @param  ptTimer: a timer handler pointer;
 * @param  timerfd: task fd which return by bTimerAddTask
 * @return return 0 is success, or -1 if an error occurred(in which case, errno is set appropriately);
 */
int bTimerDeleteTask(bTimer_t *ptTimer, int timerfd);

/**
 * @brief  Timer Release function, Release some resource;
 * @param  ptTimer: a timer handler pointer;
 * @return no return value;
 */
void bTimerRelease(bTimer_t *ptTimer);

/**
 * @brief  Get Task remain repeat count
 * @param  ptTimer: a timer handler pointer;
 * @param  timerfd: a task timerfd handler;
 * @param  remainingCnt: a uint64_t pointer to Remaining Trigger Count
 * @return if failed -1 is return, else 0 for successful 
 */
int bTimerGetRemainingCount(bTimer_t *ptTimer, int timerfd, int *remainingCnt);

/**
 * @brief  Get Task remain time in ms
 * @param  ptTimer: a timer handler pointer;
 * @param  timerfd: a task timerfd handler;
 * @param  remainingMs: a uint64_t pointer to store interval value;
 * @return return zero if successful, return -1 if failed
 */
int bTimerGetRemainingTime(bTimer_t *ptTimer, int timerfd, uint64_t *remainingMs) ;

/**
 * @brief Get the timestamp (in milliseconds) of the next time the timer is triggered
 * @param ptTimer Timer Manager Pointer
 * @param timerfd Scheduled task handle
 * @param nextTrigger Next trigger timestamp (milliseconds) output parameter
 * @return 0:success -1:fail
 */
int bTimerGetNextTriggerTime(bTimer_t *ptTimer, int timerfd, uint64_t *nextTrigger);

/**
 * @brief Get the interval (in milliseconds) of task
 * @param ptTimer Timer Manager Pointer
 * @param timerfd Scheduled task handle
 * @param intervalMs interval timestamp (milliseconds) output parameter
 * @return 0:success -1:fail
 */
int bTimerGetInterval(bTimer_t *ptTimer, int timerfd, uint64_t *intervalMs);

/**
 * @brief  Get Timer task count
 * @param  ptTimer: a timer handler pointer;
 * @return return a value of task count;
 */
int bTimerGetTaskCnt(bTimer_t *ptTimer);


#ifdef __cplusplus
}
#endif

#endif