/********************************************************************************
* ZHT Communication & technology Co.Ltd 2019-2029 Copyright reserved
* File : bMmio.h
* Function : Base on linux Memory-mapped I/O (MMIO), used to implement reading and writing of physical addresses
* Author : yangzuogui
* Created on: 2025.07.14
*
* Modification history:
* Ver Who Date Changes
* ------------------------------------------------------------------------------
* V1.0 yangzuogui 2025/07/14 First release
********************************************************************************/
#ifndef __B_MMIO_H__
#define __B_MMIO_H__

#ifdef __cplusplus
extern "C" {
#endif

#include <stdint.h>


/**
 * @brief MMIO error code definition
 */
typedef enum bMmioResult {
    mmioResult_eSUCCESS      = 0,      /**< Success */
    mmioResult_eERROR_ARG    = -1,     /**< Invalid argument */
    mmioResult_eERROR_OPEN   = -2,     /**< Failed to open /dev/mem */
    mmioResult_eERROR_MMAP   = -3,     /**< mmap failed */
    mmioResult_eERROR_UNMAP  = -4,     /**< munmap failed */
    mmioResult_eERROR_RANGE  = -5,     /**< Out of range */
} mmioResult_e;

/**
 * @brief MMIO device structure
 */
typedef struct bMmioDev bMmioDev_t;

/**
 * @brief Create a MMIO device object
 * @param base Physical base address
 * @param size Mapping size
 * @return bMmioDev_t* handle, NULL if failed
 */
bMmioDev_t *bMmioDevCreate(uintptr_t base, size_t size);

/**
 * @brief Release a MMIO device object
 * @param ptMmioDev Device handle
 * @return 0 if success, negative value if failed
 */
int32_t bMmioDevRelease(bMmioDev_t *ptMmioDev);

/**
 * @brief Read 32-bit data
 * @param ptMmioDev Device handle
 * @param offset Offset
 * @param value Pointer to store the read value
 * @return 0 if success, negative value if failed
 */
int bMmioDevRead32(bMmioDev_t *ptMmioDev, uintptr_t offset, uint32_t *value);

/**
 * @brief Read 16-bit data
 * @param ptMmioDev Device handle
 * @param offset Offset
 * @param value Pointer to store the read value
 * @return 0 if success, negative value if failed
 */
int bMmioDevRead16(bMmioDev_t *ptMmioDev, uintptr_t offset, uint16_t *value);

/**
 * @brief Read 8-bit data
 * @param ptMmioDev Device handle
 * @param offset Offset
 * @param value Pointer to store the read value
 * @return 0 if success, negative value if failed
 */
int bMmioDevRead8(bMmioDev_t *ptMmioDev, uintptr_t offset, uint8_t *value);

/**
 * @brief Read arbitrary length data
 * @param ptMmioDev Device handle
 * @param offset Offset
 * @param buf Buffer to store the read data
 * @param len Length to read
 * @return 0 if success, negative value if failed
 */
int bMmioDevRead(bMmioDev_t *ptMmioDev, uintptr_t offset, uint8_t *buf, size_t len);

/**
 * @brief Write 32-bit data
 * @param ptMmioDev Device handle
 * @param offset Offset
 * @param value Value to write
 * @return 0 if success, negative value if failed
 */
int bMmioDevWrite32(bMmioDev_t *ptMmioDev, uintptr_t offset, uint32_t value);

/**
 * @brief Write 16-bit data
 * @param ptMmioDev Device handle
 * @param offset Offset
 * @param value Value to write
 * @return 0 if success, negative value if failed
 */
int bMmioDevWrite16(bMmioDev_t *ptMmioDev, uintptr_t offset, uint16_t value);

/**
 * @brief Write 8-bit data
 * @param ptMmioDev Device handle
 * @param offset Offset
 * @param value Value to write
 * @return 0 if success, negative value if failed
 */
int bMmioDevWrite8(bMmioDev_t *ptMmioDev, uintptr_t offset, uint8_t value);

/**
 * @brief Write arbitrary length data
 * @param ptMmioDev Device handle
 * @param offset Offset
 * @param buf Buffer to write
 * @param len Length to write
 * @return 0 if success, negative value if failed
 */
int bMmioDevWrite(bMmioDev_t *ptMmioDev, uintptr_t offset, const uint8_t *buf, size_t len);

/**
 * @brief Get physical base address
 * @param ptMmioDev Device handle
 * @return Physical base address
 */
uintptr_t bMmioDevGetBase(bMmioDev_t *ptMmioDev);

/**
 * @brief Get mapping size
 * @param ptMmioDev Device handle
 * @return Mapping size
 */
size_t bMmioDevGetSize(bMmioDev_t *ptMmioDev);

#ifdef __cplusplus
}
#endif

#endif /* __B_MMIO_H__ */