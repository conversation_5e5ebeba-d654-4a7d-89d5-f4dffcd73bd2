/********************************************************************************
 * ZHT Communication & technology Co.Ltd 2019-2029 Copyright reserved
 * File : bMmio.c
 * Function : Base on linux Memory-mapped I/O (MMIO), used to implement reading and writing of physical addresses
 * Author : yangzuogui
 * Created on: 2025.07.14
 *
 * Modification history:
 * Ver Who Date Changes
 * ------------------------------------------------------------------------------
 * V1.0 yangzuogui 2025/07/14 First release
 ********************************************************************************/
#include <stdint.h>
#include <stdarg.h>
#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <sys/mman.h>
#include <errno.h>
#include <fcntl.h>
#include <unistd.h>
#include "bMmio.h"

// #define _B_MMIO_DEBUG
#ifdef _B_MMIO_DEBUG
#define BMMIO_DEBUG(...) fprintf(stderr, "[BMMIO] "__VA_ARGS__);
#define BMMIO_INFO(...) fprintf(stderr, "[BMMIO] "__VA_ARGS__);
#define BMMIO_ERROR(...) fprintf(stderr, "[BMMIO] "__VA_ARGS__);
#else
#define BMMIO_DEBUG(...)
#define BMMIO_INFO(...)
#define BMMIO_ERROR(...)
#endif

struct bMmioDev {
    uintptr_t base;        // phy addr base
    uintptr_t alignedBase; // phy addr base-aligned
    size_t size;           // phy addr size
    size_t alignedSize;    // phy addr size-aligned
    void *ptr;
};

/**
 * @brief Create a MMIO device object
 * @param base Physical base address
 * @param size Mapping size
 * @return bMmioDev_t* handle, NULL if failed
 */
bMmioDev_t *bMmioDevCreate(uintptr_t base, size_t size)
{
    if (size == 0) {
        BMMIO_ERROR("Invalid size: 0\n");
        return NULL;
    }
    int fd = -1;
    bMmioDev_t *ptMmioDev = (bMmioDev_t *)calloc(1, sizeof(bMmioDev_t));
    if (!ptMmioDev) {
        BMMIO_ERROR("Memory allocation failed\n");
        return NULL;
    }
    ptMmioDev->base = base;
    ptMmioDev->size = size;
    ptMmioDev->alignedBase = ptMmioDev->base - (ptMmioDev->base % sysconf(_SC_PAGESIZE));
    ptMmioDev->alignedSize = ptMmioDev->size + (ptMmioDev->base - ptMmioDev->alignedBase);

    /* Open /dev/mem */
    fd = open("/dev/mem", O_RDWR | O_SYNC);
    if (fd < 0) {
        BMMIO_ERROR("Failed to open /dev/mem: %s\n", strerror(errno));
        free(ptMmioDev);
        return NULL;
    }

    /* Map physical memory */
    ptMmioDev->ptr = mmap(0, ptMmioDev->alignedSize, PROT_READ | PROT_WRITE, MAP_SHARED, fd, ptMmioDev->alignedBase);
    if (ptMmioDev->ptr == MAP_FAILED) {
        BMMIO_ERROR("mmap failed: %s\n", strerror(errno));
        close(fd);
        free(ptMmioDev);
        return NULL;
    }

    /* Close file descriptor */
    if (close(fd) < 0) {
        BMMIO_ERROR("Failed to close fd: %s\n", strerror(errno));
        munmap(ptMmioDev->ptr, ptMmioDev->alignedSize);
        ptMmioDev->ptr = NULL;
        free(ptMmioDev);
        return NULL;
    }
    BMMIO_DEBUG("MMIO device created: base=0x%lx, size=%zu, alignedBase=0x%lx, alignedSize=%zu\n", ptMmioDev->base, ptMmioDev->size, ptMmioDev->alignedBase, ptMmioDev->alignedSize);
    return ptMmioDev;
}

/**
 * @brief Release a MMIO device object
 * @param ptMmioDev Device handle
 * @return 0 if success, negative value if failed
 */
int32_t bMmioDevRelease(bMmioDev_t *ptMmioDev)
{
    if (ptMmioDev == NULL) {
        BMMIO_ERROR("Release called with NULL handle\n");
        return mmioResult_eERROR_ARG;
    }
    if (!ptMmioDev->ptr) {
        BMMIO_DEBUG("Release called, but memory already unmapped\n");
        free(ptMmioDev);
        return mmioResult_eSUCCESS;
    }
    /* Unmap memory */
    if (munmap(ptMmioDev->ptr, ptMmioDev->alignedSize) < 0) {
        BMMIO_ERROR("munmap failed: %s\n", strerror(errno));
        free(ptMmioDev);
        return mmioResult_eERROR_UNMAP;
    }
    ptMmioDev->ptr = NULL;
    BMMIO_DEBUG("MMIO device released\n");
    free(ptMmioDev);
    return mmioResult_eSUCCESS;
}

/**
 * @brief Read 32-bit data
 * @param ptMmioDev Device handle
 * @param offset Offset
 * @param value Pointer to store the read value
 * @return 0 if success, negative value if failed
 */
int bMmioDevRead32(bMmioDev_t *ptMmioDev, uintptr_t offset, uint32_t *value)
{
    if (!ptMmioDev || !ptMmioDev->ptr || !value) {
        BMMIO_ERROR("bMmioDevRead32: invalid argument\n");
        return mmioResult_eERROR_ARG;
    }
    offset += (ptMmioDev->base - ptMmioDev->alignedBase);
    if ((offset + 4) > ptMmioDev->alignedSize) {
        BMMIO_ERROR("bMmioDevRead32: out of range (offset=0x%lx, size=%zu)\n", offset, ptMmioDev->alignedSize);
        return mmioResult_eERROR_RANGE;
    }
    *value = *(volatile uint32_t *)(((volatile uint8_t *)ptMmioDev->ptr) + offset);
    return mmioResult_eSUCCESS;
}

/**
 * @brief Read 16-bit data
 * @param ptMmioDev Device handle
 * @param offset Offset
 * @param value Pointer to store the read value
 * @return 0 if success, negative value if failed
 */
int bMmioDevRead16(bMmioDev_t *ptMmioDev, uintptr_t offset, uint16_t *value)
{
    if (!ptMmioDev || !ptMmioDev->ptr || !value) {
        BMMIO_ERROR("bMmioDevRead16: invalid argument\n");
        return mmioResult_eERROR_ARG;
    }
    offset += (ptMmioDev->base - ptMmioDev->alignedBase);
    if ((offset + 2) > ptMmioDev->alignedSize) {
        BMMIO_ERROR("bMmioDevRead16: out of range (offset=0x%lx, size=%zu)\n", offset, ptMmioDev->alignedSize);
        return mmioResult_eERROR_RANGE;
    }
    *value = *(volatile uint16_t *)(((volatile uint8_t *)ptMmioDev->ptr) + offset);
    return mmioResult_eSUCCESS;
}

/**
 * @brief Read 8-bit data
 * @param ptMmioDev Device handle
 * @param offset Offset
 * @param value Pointer to store the read value
 * @return 0 if success, negative value if failed
 */
int bMmioDevRead8(bMmioDev_t *ptMmioDev, uintptr_t offset, uint8_t *value)
{
    if (!ptMmioDev || !ptMmioDev->ptr || !value) {
        BMMIO_ERROR("bMmioDevRead8: invalid argument\n");
        return mmioResult_eERROR_ARG;
    }
    offset += (ptMmioDev->base - ptMmioDev->alignedBase);
    if ((offset + 1) > ptMmioDev->alignedSize) {
        BMMIO_ERROR("bMmioDevRead8: out of range (offset=0x%lx, size=%zu)\n", offset, ptMmioDev->alignedSize);
        return mmioResult_eERROR_RANGE;
    }
    *value = *(volatile uint8_t *)(((volatile uint8_t *)ptMmioDev->ptr) + offset);
    return mmioResult_eSUCCESS;
}

/**
 * @brief Read arbitrary length data
 * @param ptMmioDev Device handle
 * @param offset Offset
 * @param buf Buffer to store the read data
 * @param len Length to read
 * @return 0 if success, negative value if failed
 */
int bMmioDevRead(bMmioDev_t *ptMmioDev, uintptr_t offset, uint8_t *buf, size_t len)
{
    if (!ptMmioDev || !ptMmioDev->ptr || !buf || len == 0) {
        BMMIO_ERROR("bMmioDevRead: invalid argument\n");
        return mmioResult_eERROR_ARG;
    }
    offset += (ptMmioDev->base - ptMmioDev->alignedBase);
    if ((offset + len) > ptMmioDev->alignedSize) {
        BMMIO_ERROR("bMmioDevRead: out of range (offset=0x%lx, len=%zu, size=%zu)\n", offset, len, ptMmioDev->alignedSize);
        return mmioResult_eERROR_RANGE;
    }
    memcpy((void *)buf, (const void *)(((volatile uint8_t *)ptMmioDev->ptr) + offset), len);
    return mmioResult_eSUCCESS;
}

/**
 * @brief Write 32-bit data
 * @param ptMmioDev Device handle
 * @param offset Offset
 * @param value Value to write
 * @return 0 if success, negative value if failed
 */
int bMmioDevWrite32(bMmioDev_t *ptMmioDev, uintptr_t offset, uint32_t value)
{
    if (!ptMmioDev || !ptMmioDev->ptr) {
        BMMIO_ERROR("bMmioDevWrite32: invalid argument\n");
        return mmioResult_eERROR_ARG;
    }
    offset += (ptMmioDev->base - ptMmioDev->alignedBase);
    if ((offset + 4) > ptMmioDev->alignedSize) {
        BMMIO_ERROR("bMmioDevWrite32: out of range (offset=0x%lx, size=%zu)\n", offset, ptMmioDev->alignedSize);
        return mmioResult_eERROR_RANGE;
    }
    *(volatile uint32_t *)(((volatile uint8_t *)ptMmioDev->ptr) + offset) = value;
    return mmioResult_eSUCCESS;
}

/**
 * @brief Write 16-bit data
 * @param ptMmioDev Device handle
 * @param offset Offset
 * @param value Value to write
 * @return 0 if success, negative value if failed
 */
int bMmioDevWrite16(bMmioDev_t *ptMmioDev, uintptr_t offset, uint16_t value)
{
    if (!ptMmioDev || !ptMmioDev->ptr) {
        BMMIO_ERROR("bMmioDevWrite16: invalid argument\n");
        return mmioResult_eERROR_ARG;
    }
    offset += (ptMmioDev->base - ptMmioDev->alignedBase);
    if ((offset + 2) > ptMmioDev->alignedSize) {
        BMMIO_ERROR("bMmioDevWrite16: out of range (offset=0x%lx, size=%zu)\n", offset, ptMmioDev->alignedSize);
        return mmioResult_eERROR_RANGE;
    }
    *(volatile uint16_t *)(((volatile uint8_t *)ptMmioDev->ptr) + offset) = value;
    return mmioResult_eSUCCESS;
}

/**
 * @brief Write 8-bit data
 * @param ptMmioDev Device handle
 * @param offset Offset
 * @param value Value to write
 * @return 0 if success, negative value if failed
 */
int bMmioDevWrite8(bMmioDev_t *ptMmioDev, uintptr_t offset, uint8_t value)
{
    if (!ptMmioDev || !ptMmioDev->ptr) {
        BMMIO_ERROR("bMmioDevWrite8: invalid argument\n");
        return mmioResult_eERROR_ARG;
    }
    offset += (ptMmioDev->base - ptMmioDev->alignedBase);
    if ((offset + 1) > ptMmioDev->alignedSize) {
        BMMIO_ERROR("bMmioDevWrite8: out of range (offset=0x%lx, size=%zu)\n", offset, ptMmioDev->alignedSize);
        return mmioResult_eERROR_RANGE;
    }
    *(volatile uint8_t *)(((volatile uint8_t *)ptMmioDev->ptr) + offset) = value;
    return mmioResult_eSUCCESS;
}

/**
 * @brief Write arbitrary length data
 * @param ptMmioDev Device handle
 * @param offset Offset
 * @param buf Buffer to write
 * @param len Length to write
 * @return 0 if success, negative value if failed
 */
int bMmioDevWrite(bMmioDev_t *ptMmioDev, uintptr_t offset, const uint8_t *buf, size_t len)
{
    if (!ptMmioDev || !ptMmioDev->ptr || !buf || len == 0) {
        BMMIO_ERROR("bMmioDevWrite: invalid argument\n");
        return mmioResult_eERROR_ARG;
    }
    offset += (ptMmioDev->base - ptMmioDev->alignedBase);
    if ((offset + len) > ptMmioDev->alignedSize) {
        BMMIO_ERROR("bMmioDevWrite: out of range (offset=0x%lx, len=%zu, size=%zu)\n", offset, len, ptMmioDev->alignedSize);
        return mmioResult_eERROR_RANGE;
    }
    memcpy((void *)(((volatile uint8_t *)ptMmioDev->ptr) + offset), (const void *)buf, len);
    return mmioResult_eSUCCESS;
}

/**
 * @brief Get physical base address
 * @param ptMmioDev Device handle
 * @return Physical base address
 */
uintptr_t bMmioDevGetBase(bMmioDev_t *ptMmioDev)
{
    if (!ptMmioDev) {
        BMMIO_ERROR("bMmioDevGetBase: invalid argument\n");
        return mmioResult_eSUCCESS;
    }
    return ptMmioDev->base;
}

/**
 * @brief Get mapping size
 * @param ptMmioDev Device handle
 * @return Mapping size
 */
size_t bMmioDevGetSize(bMmioDev_t *ptMmioDev)
{
    if (!ptMmioDev) {
        BMMIO_ERROR("bMmioDevGetSize: invalid argument\n");
        return mmioResult_eSUCCESS;
    }
    return ptMmioDev->size;
}
