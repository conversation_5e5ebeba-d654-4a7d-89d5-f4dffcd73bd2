#include "bSpi.h"
#include <limits.h>
#include <sys/ioctl.h>
#include <linux/spi/spidev.h>

// SPI设备结构体
typedef struct bSpiDev {
    int fd;              // 文件描述符
    char *devPath;       // 设备路径
    uint32_t   speed;    // 通信速率
    uint8_t bits;        // 位宽
    bSpiMode_e mode;     // 工作模式
};

// 增加位宽校验宏
#define SPI_BITS_VALID(b) ((b) == 8 || (b) == 16 || (b) == 32)

/**
 * @brief Open SPI device
 * @param devPath SPI device path, such as "/dev/spidev0.0"
 * @param mode SPI working mode
 * @param speed Communication rate, unit is Hz
 * @param bits Bit width, usually 8    
 * @return If success, return SPI device struct pointer, otherwise return NULL
 */
bSpiDev_t *bSpiOpen(const char *devPath, bSpiMode_e mode, uint32_t speed, uint8_t bits)
{
    if(!devPath || !SPI_BITS_VALID(bits)){
        return NULL;
    }

    bSpiDev_t *dev = malloc(sizeof(bSpiDev_t));
    if (!dev) {
        return NULL;
    }

    dev->devPath = strdup(devPath);
    if (!dev->devPath) {
        free(dev);
        return NULL;
    }

    dev->fd = open(dev->devPath, O_RDWR);
    if (dev->fd < 0) {
        free(dev->devPath);
        free(dev);
        return NULL;
    }

    // 设置SPI设备参数
    uint8_t tmpMode = (uint8_t)mode;
    if(ioctl(dev->fd, SPI_IOC_WR_MODE, &tmpMode) < 0 || \
       ioctl(dev->fd, SPI_IOC_WR_BITS_PER_WORD, &bits) < 0 || \
       ioctl(dev->fd, SPI_IOC_WR_MAX_SPEED_HZ, &speed) < 0){
        close(dev->fd);
        free(dev->devPath);
        free(dev);
        return NULL;
    }

    dev->mode = mode;
    dev->speed = speed;
    dev->bits = bits;

    return dev;
}

/**
 * @brief Close SPI device
 * @param dev SPI device struct pointer
 */
void bSpiClose(bSpiDev_t *dev)
{
    if (dev != NULL) {
        if(dev->fd >= 0){
            close(dev->fd);
            dev->fd = -1;
        }
        free(dev->devPath);
        free(dev);
    }
}

/**
 * @brief Set SPI device working mode
 * @param dev SPI device struct pointer
 * @param mode SPI working mode
 * @return If success, return 0, otherwise return -1
 */
int bSpiSetMode(bSpiDev_t *dev, const bSpiMode_e mode)
{
    if (!dev || dev->fd < 0) {
        return -1;
    }

    uint8_t tmpMode = (uint8_t)mode;
    int ret = ioctl(dev->fd, SPI_IOC_WR_MODE, &tmpMode);
    if (ret < 0) {
        return -1;
    }

    dev->mode = mode;
    return 0;
}

/**
 * @brief Get SPI device working mode
 * @param dev SPI device struct pointer
 * @param mode Pointer to SPI working mode
 * @return If success, return 0, otherwise return -1
 */
int bSpiGetMode(bSpiDev_t *dev, bSpiMode_e *mode)
{
    if (!dev || dev->fd < 0 || !mode) {
        return -1;
    }

    uint8_t tmpMode = 0;
    int ret = ioctl(dev->fd, SPI_IOC_RD_MODE, &tmpMode);
    if (ret < 0) {
        return -1;
    }

    *mode = (bSpiMode_e)tmpMode;

    return 0;
}

/**
 * @brief Set SPI device communication rate
 * @param dev SPI device struct pointer
 * @param speed Communication rate, unit is Hz
 * @return If success, return 0, otherwise return -1
 */
int bSpiSetSpeed(bSpiDev_t *dev, const uint32_t speed)
{
    if (!dev || dev->fd < 0) {
        return -1;
    }

    int ret = ioctl(dev->fd, SPI_IOC_WR_MAX_SPEED_HZ, &speed);
    if (ret < 0) {
        return -1;
    }

    dev->speed = speed;
    return 0;
}

/**
 * @brief Get SPI device communication rate
 * @param dev SPI device struct pointer
 * @param speed Pointer to communication rate, unit is Hz
 * @return If success, return 0, otherwise return -1
 */
int bSpiGetSpeed(bSpiDev_t *dev, uint32_t *speed)
{
    if (!dev || dev->fd < 0 || !speed) {
        return -1;
    }

    int ret = ioctl(dev->fd, SPI_IOC_RD_MAX_SPEED_HZ, speed);
    if (ret < 0) {
        return -1;
    }

    return 0;
}

/**
 * @brief Set SPI device bit width
 * @param dev SPI device struct pointer
 * @param bits Bit width, usually 8
 * @return If success, return 0, otherwise return -1
 */
int bSpiSetBits(bSpiDev_t *dev, const uint8_t bits)
{
    if (!dev || dev->fd < 0 || !SPI_BITS_VALID(bits)) {
        return -1;
    }

    int ret = ioctl(dev->fd, SPI_IOC_WR_BITS_PER_WORD, &bits);
    if (ret < 0) {
        return -1;
    }

    dev->bits = bits;
    return 0;
}

/**
 * @brief Get SPI device bit width
 * @param dev SPI device struct pointer
 * @param bits Pointer to bit width, usually 8
 * @return If success, return 0, otherwise return -1
 */
int bSpiGetBits(bSpiDev_t *dev, uint8_t *bits)
{
    if (!dev || dev->fd < 0 || !bits) {
        return -1;
    }

    int ret = ioctl(dev->fd, SPI_IOC_RD_BITS_PER_WORD, bits);
    if (ret < 0) {
        return -1;
    }

    return 0;
}

/**
 * @brief Get SPI device path
 * @param dev SPI device struct pointer
 * @return Pointer to path
 */
const char* bSpiGetDevPath(bSpiDev_t *dev)
{
    return dev ? dev->devPath : NULL;
}

/**
 * @brief Full-duplex data transfer
 * @param dev SPI device struct pointer
 * @param tx_buf Send buffer
 * @param rx_buf Receive buffer
 * @param len Data length
 * @return If success, return the number of bytes transferred, otherwise return -1
 */
int bSpiTransfer(bSpiDev_t *dev, const uint8_t *txBuf, uint8_t *rxBuf, unsigned int len)
{
    if (!dev || dev->fd < 0 || len == 0 || (!txBuf && !rxBuf)) {
        return -1;
    }


    struct spi_ioc_transfer tr = {
        .tx_buf = (void *)txBuf,
        .rx_buf = (void *)rxBuf,
        .len = len,
        .speed_hz = dev->speed,
        .bits_per_word = dev->bits,
        .cs_change = 0,
        .delay_usecs = 0
    };

    int ret = ioctl(dev->fd, SPI_IOC_MESSAGE(1), &tr);
    if (ret < 0) {
        return -1;
    }

    return len;
}

/**
 * @brief Send data only
 * @param dev SPI device struct pointer
 * @param buf Send buffer
 * @param len Data length
 * @return If success, return the number of bytes sent, otherwise return -1
 */
int bSpiSend(bSpiDev_t *dev, const uint8_t *buf, unsigned int len)
{
    if (!dev || dev->fd < 0 || !buf || len == 0) {
        return -1;
    }

    return bSpiTransfer(dev, buf, NULL, len);
}

/**
 * @brief Receive data only
 * @param dev SPI device struct pointer
 * @param buf Receive buffer
 * @param len Data length
 * @return If success, return the number of bytes received, otherwise return -1
 */
int bSpiReceive(bSpiDev_t *dev, uint8_t *buf, unsigned int len)
{
    if (!dev || dev->fd < 0 || !buf || len == 0) {
        return -1;
    }

    return bSpiTransfer(dev, NULL, buf, len);
}
