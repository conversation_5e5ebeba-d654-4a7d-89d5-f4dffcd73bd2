/*******************************************************************************
* ZHT Communication & technology Co.Ltd 2019-2029 Copyright reserved
* File : bSpi.h
* Function : Base on linux spidev, a useful spi lib
* Author : zhenmingwei
* Created on: 2025.07.29
*
* Modification history:
* Ver Who Date Changes
* ------------------------------------------------------------------------------
* V1.0 zhenmingwei 2025/07/29 First release
********************************************************************************/
#ifndef _B_SPI_H_
#define _B_SPI_H_

#include <stdint.h>
#include <stddef.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <fcntl.h>
#include <unistd.h>
#include <stdbool.h>

// SPI模式枚举
typedef enum {
    SPI_MODE_0 = 0,  // CPOL=0, CPHA=0
    SPI_MODE_1,      // CPOL=0, CPHA=1
    SPI_MODE_2,      // CPOL=1, CPHA=0
    SPI_MODE_3       // CPOL=1, CPHA=1
} bSpiMode_e;

typedef struct bSpiDev bSpiDev_t;

/**
 * @brief Open SPI device
 * @param devPath SPI device path, such as "/dev/spidev0.0"
 * @param mode SPI working mode
 * @param speed Communication rate, unit is Hz
 * @param bits Bit width, usually 8    
 * @return If success, return SPI device struct pointer, otherwise return NULL
 */
bSpiDev_t *bSpiOpen(const char *devPath, bSpiMode_e mode, uint32_t speed, uint8_t bits);

/**
 * @brief Close SPI device
 * @param dev SPI device struct pointer
 */
void bSpiClose(bSpiDev_t *dev);

/**
 * @brief Set SPI device working mode
 * @param dev SPI device struct pointer
 * @param mode SPI working mode
 * @return If success, return 0, otherwise return -1
 */
int bSpiSetMode(bSpiDev_t *dev, const bSpiMode_e mode);

/**
 * @brief Get SPI device working mode
 * @param dev SPI device struct pointer
 * @param mode Pointer to SPI working mode
 * @return If success, return 0, otherwise return -1
 */
int bSpiGetMode(bSpiDev_t *dev, bSpiMode_e *mode);

/**
 * @brief Set SPI device communication rate
 * @param dev SPI device struct pointer
 * @param speed Communication rate, unit is Hz
 * @return If success, return 0, otherwise return -1
 */
int bSpiSetSpeed(bSpiDev_t *dev, const uint32_t speed);

/**
 * @brief Get SPI device communication rate
 * @param dev SPI device struct pointer
 * @param speed Pointer to communication rate, unit is Hz
 * @return If success, return 0, otherwise return -1
 */
int bSpiGetSpeed(bSpiDev_t *dev, uint32_t *speed);

/**
 * @brief Set SPI device bit width
 * @param dev SPI device struct pointer
 * @param bits Bit width, usually 8
 * @return If success, return 0, otherwise return -1
 */
int bSpiSetBits(bSpiDev_t *dev, const uint8_t bits);

/**
 * @brief Get SPI device bit width
 * @param dev SPI device struct pointer
 * @param bits Pointer to bit width, usually 8
 * @return If success, return 0, otherwise return -1
 */
int bSpiGetBits(bSpiDev_t *dev, uint8_t *bits);

/**
 * @brief Get SPI device path
 * @param dev SPI device struct pointer
 * @return Pointer to path
 */
const char* bSpiGetDevPath(bSpiDev_t *dev);

/**
 * @brief Full-duplex data transfer
 * @param dev SPI device struct pointer
 * @param txBuf Send buffer
 * @param rxBuf Receive buffer
 * @param len Data length
 * @return If success, return the number of bytes transferred, otherwise return -1
 */
int bSpiTransfer(bSpiDev_t *dev, const uint8_t *txBuf, uint8_t *rxBuf, unsigned int len);

/**
 * @brief Send data only
 * @param dev SPI device struct pointer
 * @param buf Send buffer
 * @param len Data length
 * @return If success, return the number of bytes sent, otherwise return -1
 */
int bSpiSend(bSpiDev_t *dev, const uint8_t *buf, unsigned int len);   

/**
 * @brief Receive data only
 * @param dev SPI device struct pointer
 * @param buf Receive buffer
 * @param len Data length
 * @return If success, return the number of bytes received, otherwise return -1
 */
int bSpiReceive(bSpiDev_t *dev, uint8_t *buf, unsigned int len);


#endif
