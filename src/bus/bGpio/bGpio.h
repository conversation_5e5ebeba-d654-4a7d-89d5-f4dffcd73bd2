/*******************************************************************************
* ZHT Communication & technology Co.Ltd 2019-2029 Copyright reserved
* File : bGpio.h
* Function : Base on linux sysfs API to Access GPIO
* Author : yangzuogui
* Created on: 2025.07.11
*
* Modification history:
* Ver Who Date Changes
* ------------------------------------------------------------------------------
* V1.0 yangzuogui 2025/07/11 First release
********************************************************************************/
#ifndef __B_GPIO_H__
#define __B_GPIO_H__
#include <stdint.h>

#ifdef __cplusplus
extern "C" {
#endif

// GPIO pin maximum value
#define GPIO_PIN_MAX    1999

// GPIO error code enumeration
typedef enum gpioResult {
    gpioResult_eSUCCESS                = 0,      // Success
    gpioResult_eERROR_INVALID_PIN      = -1,     // Invalid GPIO pin number
    gpioResult_eERROR_NULL_PTR         = -2,     // Null pointer error
    gpioResult_eERROR_INVALID_PARAM    = -3,     // Invalid parameter
    gpioResult_eERROR_FILE_ACCESS      = -4,     // File access error
    gpioResult_eERROR_READ_FAILED      = -5,     // Read failed
    gpioResult_eERROR_WRITE_FAILED     = -6,     // Write failed
    gpioResult_eERROR_EXPORT_FAILED    = -7,     // GPIO export failed
    gpioResult_eERROR_UNEXPORT_FAILED  = -8,     // GPIO unexport failed
} gpioResult_e;

typedef enum gpioEdge {
    gpioEdge_eNONE = 0,
    gpioEdge_eRISING,
    gpioEdge_eFALLING,
    gpioEdge_eBOTH,
}gpioEdge_e;

typedef enum gpioDir {
    gpioDir_eINPUT  = 0,
    gpioDir_eOUTPUT = 1,
}gpioDir_e;

typedef enum gpioLevel {
    gpioLevel_eLOW  = 0,
    gpioLevel_eHIGH = 1,
}gpioLevel_e;

/**
 * @brief Export GPIO pin.
 * @param pin GPIO pin number.
 * @return gpioResult_e GPIO export status.
 */
gpioResult_e bGpioExport(uint32_t pin);

/**
 * @brief Unexport GPIO pin.
 * @param pin GPIO pin number.
 * @return gpioResult_e GPIO unexport status.
 */
gpioResult_e bGpioUnexport(uint32_t pin);

/**
 * @brief Set GPIO as input.
 * @param pin GPIO pin number.
 * @return gpioResult_e GPIO set input status.
 */
gpioResult_e bGpioSetInput(uint32_t pin);

/**
 * @brief Set GPIO edge trigger.
 * @param pin GPIO pin number.
 * @param type gpioEdge_e type.
 * @return gpioResult_e GPIO set edge status.
 */
gpioResult_e bGpioSetEdge(uint32_t pin, gpioEdge_e type);

/**
 * @brief Set GPIO as output with output value.
 * @param pin GPIO pin number.
 * @param level gpioLevel_e level.
 * @return gpioResult_e GPIO set output status.
 */
gpioResult_e bGpioSetOutput(uint32_t pin, gpioLevel_e level);

/**
 * @brief Read GPIO value.
 * @param pin GPIO pin number.
 * @param level gpioLevel_e pointer to store the value.
 * @return gpioResult_e GPIO get value status.
 */
gpioResult_e bGpioGetValue(uint32_t pin, gpioLevel_e *level);

/**
 * @brief Set GPIO as output with output value.
 * @param pin GPIO pin number.
 * @param level gpioLevel_e level.
 * @return gpioResult_e GPIO set direction output status.
 */
gpioResult_e bGpioSetDirOutput(uint32_t pin, gpioLevel_e level);

/**
 * @brief Get GPIO direction.
 * @param pin GPIO pin number.
 * @param direction gpioDir_e pointer to store the direction.
 * @return gpioResult_e GPIO get direction status.
 */
gpioResult_e bGpioGetDirection(uint32_t pin, gpioDir_e *direction);

#ifdef __cplusplus
}
#endif

#endif