/*******************************************************************************
* ZHT Communication & technology Co.Ltd 2019-2029 Copyright reserved
* File : bGpio.c
* Function : Base on linux sysfs API to Access GPIO
* Author : yangzuogui
* Created on: 2025.07.11
*
* Modification history:
* Ver Who Date Changes
* ------------------------------------------------------------------------------
* V1.0 yangzuogui 2025/07/11 First release
********************************************************************************/
#include <stdio.h>
#include <errno.h>
#include <stdlib.h>
#include <stdbool.h>
#include <string.h>
#include <unistd.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <fcntl.h>
#include "bGpio.h"


// #define _B_GPIO_DEBUG
#ifdef _B_GPIO_DEBUG
#define     BGPIO_DEBUG(...)          fprintf(stderr, "[BGPIO] "__VA_ARGS__);
#define     BGPIO_INFO(...)           fprintf(stderr, "[BGPIO] "__VA_ARGS__);
#define     BGPIO_ERROR(...)          fprintf(stderr, "[BGPIO] "__VA_ARGS__);
#else
#define     BGPIO_DEBUG(...)
#define     BGPIO_INFO(...) 
#define     BGPIO_ERROR(...)
#endif

// Add missing function definitions
static bool isFileExist(const char *path)
{
    return access(path, F_OK) == 0;
}


/**
 * @brief  Read data from sysfs file
 * @param  ptPath: file path
 * @param  ptValue: buffer to store read data
 * @param  readLen: number of bytes to read
 * @return >=0: success, <0: error code
 */
static int32_t sysfsFileRead(const char *ptPath, char *ptValue, uint32_t readLen)
{
    if(access(ptPath, 0)){
        BGPIO_DEBUG("%s No such file or directory\n", ptPath);
        return -1;
    }
    
    int fd = open(ptPath, O_RDONLY);
    if (fd < 0) {
        BGPIO_DEBUG("%s: Can't open device\n", ptPath);
        return -2;
    }

    int ret = read(fd, ptValue, readLen);
    if (ret < 0) {
        BGPIO_DEBUG("%s: Can't read from file\n", ptPath);
        return -3;
    }

    ret = close(fd);
    if (ret < 0) {
        BGPIO_DEBUG("%s: Can't close device\n", ptPath);
        return -4;
    }

    return ret;
}

/**
 * @brief  Write data to sysfs file
 * @param  ptPath: file path
 * @param  ptValue: buffer to write
 * @param  writeLen: number of bytes to write
 * @return 0: success, <0: error code
 */
static int32_t sysfsFileWrite(const char *ptPath, char *ptValue, uint32_t writeLen)
{
    if(access(ptPath, 0)){
        BGPIO_DEBUG("%s No such file or directory\n", ptPath);
        return -1;
    }

    int fd = open(ptPath, O_WRONLY);
    if (fd < 0) {
        BGPIO_DEBUG("%s: Can't open device\n", ptPath);
        return -2;
    }

    int ret = write(fd, ptValue, writeLen);
    if (ret < 0) {
        BGPIO_DEBUG("%s: Can't write to file\n", ptPath);
        return -3;
    }

    ret = close(fd);
    if (ret < 0) {
        BGPIO_DEBUG("%s: Can't close device\n", ptPath);
        return -4;
    }

    return 0;
}

/**
 * @brief  Export a GPIO pin
 * @param  pin: GPIO pin number
 * @return gpioResult_e error code
 */
gpioResult_e bGpioExport(uint32_t pin)
{
    // GPIO pin number validation (usually GPIO number is between 0-GPIO_PIN_MAX)
    if (pin > GPIO_PIN_MAX) {
        return gpioResult_eERROR_INVALID_PIN;
    }
    
    char buf[128];
    memset(buf, 0, sizeof(buf));
    snprintf(buf, sizeof(buf), "/sys/class/gpio/gpio%u", pin);
    if(isFileExist(buf)){
        return gpioResult_eSUCCESS;
    }else{
        memset(buf, 0, sizeof(buf));
        int len = sprintf(buf, "%u", pin);
        int ret = sysfsFileWrite("/sys/class/gpio/export", buf, len);
        if (ret < 0) {
            return gpioResult_eERROR_EXPORT_FAILED;
        }
        return gpioResult_eSUCCESS;
    }
    return gpioResult_eSUCCESS;
}

/**
 * @brief  Unexport a GPIO pin
 * @param  pin: GPIO pin number
 * @return gpioResult_e error code
 */
gpioResult_e bGpioUnexport(uint32_t pin)
{
    // GPIO pin number validation
    if (pin > GPIO_PIN_MAX) {
        return gpioResult_eERROR_INVALID_PIN;
    }
    
    char buf[128];
    memset(buf, 0, sizeof(buf));
    snprintf(buf, sizeof(buf), "/sys/class/gpio/gpio%u", pin);
    if(isFileExist(buf)){
        memset(buf, 0, sizeof(buf));
        int len = sprintf(buf, "%u", pin);
        int ret = sysfsFileWrite("/sys/class/gpio/unexport", buf, len);
        if (ret < 0) {
            return gpioResult_eERROR_UNEXPORT_FAILED;
        }
        return gpioResult_eSUCCESS;
    }else{
        return gpioResult_eSUCCESS;
    }
    return gpioResult_eSUCCESS;
}

/**
 * @brief  Set GPIO as input
 * @param  pin: GPIO pin number
 * @return gpioResult_e error code
 */
gpioResult_e bGpioSetInput(uint32_t pin)
{
    // GPIO pin number validation
    if (pin > GPIO_PIN_MAX) {
        return gpioResult_eERROR_INVALID_PIN;
    }
    
    gpioResult_e ret = bGpioExport(pin);
    if(ret != gpioResult_eSUCCESS){
        return ret;
    }
    char path[128];
    memset(path, 0, sizeof(path));
    snprintf(path, sizeof(path), "/sys/class/gpio/gpio%u/direction", pin);
    int writeRet = sysfsFileWrite(path, "in", 3);
    if (writeRet < 0) {
        return gpioResult_eERROR_WRITE_FAILED;
    }
    return gpioResult_eSUCCESS;
}

/**
 * @brief  Set GPIO edge trigger
 * @param  pin: GPIO pin number
 * @param  type: edge type (gpioEdge_e)
 * @return gpioResult_e error code
 */
gpioResult_e bGpioSetEdge(uint32_t pin, gpioEdge_e type)
{
    // GPIO pin number validation
    if (pin > GPIO_PIN_MAX) {
        return gpioResult_eERROR_INVALID_PIN;
    }
    
    // Edge trigger type validation
    if (type < gpioEdge_eNONE || type > gpioEdge_eBOTH) {
        return gpioResult_eERROR_INVALID_PARAM;
    }
    
    gpioResult_e ret = bGpioExport(pin);
    if(ret != gpioResult_eSUCCESS){
        return ret;
    }
    ret = bGpioSetInput(pin);
    if(ret != gpioResult_eSUCCESS){
        return ret;
    }
    char path[128];
    memset(path, 0, sizeof(path));
    snprintf(path, sizeof(path), "/sys/class/gpio/gpio%u/edge", pin);
    int writeRet;
    switch (type)
    {
    case gpioEdge_eNONE:
        writeRet = sysfsFileWrite(path, "none", 5);
        break;
    case gpioEdge_eRISING:
        writeRet = sysfsFileWrite(path, "rising", 7);
        break;
    case gpioEdge_eFALLING:
        writeRet = sysfsFileWrite(path, "falling", 8);
        break;
    case gpioEdge_eBOTH:
        writeRet = sysfsFileWrite(path, "both", 5);
        break;
    default:
        return gpioResult_eERROR_INVALID_PARAM;
    }

    if (writeRet < 0) {
        return gpioResult_eERROR_WRITE_FAILED;
    }
    return gpioResult_eSUCCESS;
}

/**
 * @brief  Set GPIO as output with output value
 * @param  pin: GPIO pin number
 * @param  level: output level (gpioLevel_e)
 * @return gpioResult_e error code
 */
gpioResult_e bGpioSetOutput(uint32_t pin, gpioLevel_e level)
{
    // GPIO pin number validation
    if (pin > GPIO_PIN_MAX) {
        return gpioResult_eERROR_INVALID_PIN;
    }
    
    // Level type validation
    if (level != gpioLevel_eHIGH && level != gpioLevel_eLOW) {
        return gpioResult_eERROR_INVALID_PARAM;
    }
    
    gpioResult_e ret = bGpioExport(pin);
    if(ret != gpioResult_eSUCCESS){
        return ret;
    }
    char path[128];
    memset(path, 0, sizeof(path));
    snprintf(path, sizeof(path), "/sys/class/gpio/gpio%u/direction", pin);
    int writeRet = sysfsFileWrite(path, "out", 4);
    if (writeRet < 0) {
        return gpioResult_eERROR_WRITE_FAILED;
    }

    memset(path, 0, sizeof(path));
    snprintf(path, sizeof(path), "/sys/class/gpio/gpio%u/value", pin);
    if (level == gpioLevel_eHIGH){
        writeRet = sysfsFileWrite(path, "1", 2);
    }else{
        writeRet = sysfsFileWrite(path, "0", 2);
    }
    if (writeRet < 0) {
        return gpioResult_eERROR_WRITE_FAILED;
    }
    return gpioResult_eSUCCESS;
}

/**
 * @brief  Read GPIO value
 * @param  pin: GPIO pin number
 * @param  level: pointer to store read value (gpioLevel_e)
 * @return gpioResult_e error code
 */
gpioResult_e bGpioGetValue(uint32_t pin, gpioLevel_e *level)
{
    // Parameter validation
    if (level == NULL) {
        return gpioResult_eERROR_NULL_PTR;
    }
    
    // GPIO pin number validation
    if (pin > GPIO_PIN_MAX) {
        return gpioResult_eERROR_INVALID_PIN;
    }
    
    gpioResult_e ret = bGpioExport(pin);
    if(ret != gpioResult_eSUCCESS){
        return ret;
    }
    char path[128];
    memset(path, 0, sizeof(path));
    snprintf(path, sizeof(path), "/sys/class/gpio/gpio%u/value", pin);

    char data;
    int readRet = sysfsFileRead(path, &data, 1);
    if (readRet < 0) {
        BGPIO_ERROR("%s read failed\n", __func__);
        return gpioResult_eERROR_READ_FAILED;
    }

    // Fix logic: correctly handle read data
    if (data == '0'){
        *level = gpioLevel_eLOW;
    }else if (data == '1'){
        *level = gpioLevel_eHIGH;
    }else{
        BGPIO_ERROR("%s invalid data: %c\n", __func__, data);
        return gpioResult_eERROR_INVALID_PARAM;
    }

    return gpioResult_eSUCCESS;
}

/**
 * @brief  Set GPIO as output with output value
 * @param  pin: GPIO pin number
 * @param  level: output level (gpioLevel_e)
 * @return gpioResult_e error code
 */
gpioResult_e bGpioSetDirOutput(uint32_t pin, gpioLevel_e level)
{
    // GPIO pin number validation
    if (pin > GPIO_PIN_MAX) {
        return gpioResult_eERROR_INVALID_PIN;
    }
    
    // Level type validation
    if (level != gpioLevel_eHIGH && level != gpioLevel_eLOW) {
        return gpioResult_eERROR_INVALID_PARAM;
    }
    
    gpioResult_e ret = bGpioExport(pin);
    if(ret != gpioResult_eSUCCESS){
        return ret;
    }
    return bGpioSetOutput(pin, level);
}

/**
 * @brief  Get GPIO direction
 * @param  pin: GPIO pin number
 * @param  direction: pointer to store direction (gpioDir_e)
 * @return gpioResult_e error code
 */
gpioResult_e bGpioGetDirection(uint32_t pin, gpioDir_e *direction)
{
    // Parameter validation
    if (direction == NULL) {
        return gpioResult_eERROR_NULL_PTR;
    }
    
    // GPIO pin number validation
    if (pin > GPIO_PIN_MAX) {
        return gpioResult_eERROR_INVALID_PIN;
    }
    
    gpioResult_e ret = bGpioExport(pin);
    if(ret != gpioResult_eSUCCESS){
        return ret;
    }
    char path[128];
    memset(path, 0, sizeof(path));
    snprintf(path, sizeof(path), "/sys/class/gpio/gpio%u/direction", pin);

    char data;
    int readRet = sysfsFileRead(path, &data, 1);
    if (readRet < 0) {
        BGPIO_ERROR("%s read failed\n", __func__);
        return gpioResult_eERROR_READ_FAILED;
    }

    // Fix logic: correctly handle read data
    if (data == 'o'){
        *direction = gpioDir_eOUTPUT;
    }else if (data == 'i'){
        *direction = gpioDir_eINPUT;
    }else{
        BGPIO_ERROR("%s invalid direction data: %c\n", __func__, data);
        return gpioResult_eERROR_INVALID_PARAM;
    }

    return gpioResult_eSUCCESS;
}

