#include "bI2c.h"

struct bI2cDev
{
    int fd;              // I2C Device file descriptor
    uint8_t addr;        // I2C slave device address
    char *devPath;       // I2C device path
};

/**
 * @brief open I2C device
 * @param devPath - I2C device path
 * @param devAddr - I2C slave device address
 * @return bI2cDev_t* - success, NULL - fail
*/
bI2cDev_t *bI2cOpen(const char *devPath, const uint8_t devAddr)
{
    if (!devPath){
        return NULL;
    }

    bI2cDev_t *dev = malloc(sizeof(bI2cDev_t));
    if (!dev){
        return NULL;
    }

    dev->devPath = strdup(devPath);
    if(!dev->devPath){
        free(dev);
        return NULL;
    }

    dev->fd = open(devPath, O_RDWR);
    if (dev->fd < 0){
        free(dev->devPath);
        free(dev);
        return NULL;
    }

    if (ioctl(dev->fd, I2C_SLAVE, devAddr) < 0){
        close(dev->fd);
        free(dev->devPath);
        free(dev);
        return NULL;
    }
    dev->addr = devAddr;

    return dev;
}

/**
 * @brief get I2C device file descriptor
 * @param dev - I2C device pointer
 * @return int - file descriptor
*/
int bI2cGetFd(bI2cDev_t* dev)
{
    if(dev == NULL){
        return I2C_ERR_NOT_OPEN;
    }
    return dev->fd;
}

/**
 * @brief get I2C device path
 * @param dev - I2C device pointer
 * @return char* - device path
*/
const char* bI2cGetDevPath(bI2cDev_t* dev)
{
    if(dev == NULL){
        return NULL;
    }
    return dev->devPath;
}

/**
 * @brief get I2C slave device address
 * @param dev - I2C device pointer
 * @return int - slave address
*/
int bI2cGetDevAddr(bI2cDev_t* dev)
{
    if(dev == NULL){
        return I2C_ERR_NOT_OPEN;
    }
    return dev->addr;
}

/**
 * @brief close I2C device
 * @param dev - I2C device pointer
*/
void bI2cClose(bI2cDev_t *dev)
{
    if (dev) {
        if(dev->fd >= 0){
            close(dev->fd);
        }
        free(dev->devPath);
        free(dev);
    }
}

/**
 * @brief set I2C slave device address
 * @param dev  - device pointer
 * @param addr - slave device address
 * @return bI2cState_e - success, fail
*/
bI2cState_e bI2cSetSlaveAddr(bI2cDev_t *dev, const uint8_t addr)
{
    if (!dev || (dev->fd <= 0)) {
        return I2C_ERR_NOT_OPEN;
    }

    if(addr == dev->addr) {
        return I2C_SUCCESS;
    }

    if (ioctl(dev->fd, I2C_SLAVE, addr) < 0) {
        return I2C_ERR_ADDR_SET;
    }

    dev->addr = addr;

    return I2C_SUCCESS;
}

/**
 * @brief check I2C device function
 * @param dev   - device pointer
 * @param funcs - function pointer
 * @return bI2cState_e - success, fail
*/
bI2cState_e bI2cCheckFuncs(bI2cDev_t *dev, uint32_t *funcs)
{
    if (!dev || (dev->fd <= 0))
    {
        return I2C_ERR_NOT_OPEN;
    }

    if (ioctl(dev->fd, I2C_FUNCS, funcs) < 0)
    {
        return I2C_ERR_IOCTL;
    }

    return I2C_SUCCESS;
}

static bI2cState_e i2c_transfer(bI2cDev_t *dev, struct i2c_msg *msgs, int num_msgs) 
{
    struct i2c_rdwr_ioctl_data ioctl_data = {
        .msgs = msgs,
        .nmsgs = num_msgs
    };
    
    if (ioctl(dev->fd, I2C_RDWR, &ioctl_data) < 0) {
        return I2C_ERR_IOCTL;
    }
    
    return I2C_SUCCESS;
}

/**
 * @brief I2C write operation
 * @param dev        - device pointer
 * @param data       - data pointer
 * @param len        - data length
 * @return bI2cState_e - success, fail
*/
bI2cState_e bI2cWrite(bI2cDev_t *dev, const uint8_t *data, size_t len)
{
    if (!dev || dev->fd < 0) {
        return I2C_ERR_NOT_OPEN;
    }
    
    if (!data || len == 0) {
        return I2C_ERR_ARG;
    }
    
    struct i2c_msg msg = {
        .addr = dev->addr,
        .flags = 0, // 写操作
        .len = len,
        .buf = (uint8_t*)data
    };
    
    bI2cState_e ret = i2c_transfer(dev, &msg, 1);
    
    return ret;
}

/**
 * @brief I2C read operation
 * @param dev        - device pointer
 * @param data       - data pointer
 * @param len        - data length
 * @return bI2cState_e - success, fail
*/
bI2cState_e bI2cRead(bI2cDev_t *dev, uint8_t *data, size_t len)
{
    if (!dev || dev->fd < 0) {
        return I2C_ERR_NOT_OPEN;
    }
    
    if (!data || len == 0) {
        return I2C_ERR_ARG;
    }
    
    struct i2c_msg msg = {
        .addr = dev->addr,
        .flags = I2C_M_RD, // 读操作
        .len = len,
        .buf = data
    };
    
    bI2cState_e ret = i2c_transfer(dev, &msg, 1);
    
    return ret;
}

/**
 * @brief I2C write and read operation
 * @param dev        - device pointer
 * @param wdata      - write data pointer
 * @param wlen       - write data length
 * @param rdata      - read data pointer
 * @param rlen       - read data length
 * @return bI2cState_e - success, fail
*/
bI2cState_e bI2cWriteRead(bI2cDev_t *dev, const uint8_t *wdata, size_t wlen, uint8_t *rdata, size_t rlen) 
{
    if (!dev || dev->fd < 0) {
        return I2C_ERR_NOT_OPEN;
    }
    
    if (!wdata || wlen == 0 || !rdata || rlen == 0) {
        return I2C_ERR_ARG;
    }
    
    struct i2c_msg messages[2];
    
    // 写消息
    messages[0].addr = dev->addr;
    messages[0].flags = 0;
    messages[0].len = wlen;
    messages[0].buf = (uint8_t*)wdata;
    
    // 读消息
    messages[1].addr = dev->addr;
    messages[1].flags = I2C_M_RD;
    messages[1].len = rlen;
    messages[1].buf = rdata;
    
    bI2cState_e ret = i2c_transfer(dev, messages, 2);
    
    return ret;
}

static bI2cState_e smbusTransfer(bI2cDev_t *dev, char read_write, uint8_t command, int size, union i2c_smbus_data *data) 
{
    struct i2c_smbus_ioctl_data args;
    args.read_write = read_write;
    args.command = command;
    args.size = size;
    args.data = data;
    
    if (ioctl(dev->fd, I2C_SMBUS, &args) < 0) {
        return I2C_ERR_SMBUS;
    }
    
    return I2C_SUCCESS;
}

/**
 * @brief SMBus quick command
 * @param dev - device pointer
 * @return bI2cState_e - success, fail
*/
bI2cState_e bI2cSmbusQuick(bI2cDev_t *dev) {
    if (!dev || dev->fd < 0) {
        return I2C_ERR_NOT_OPEN;
    }
    
    union i2c_smbus_data data;
    bI2cState_e ret = smbusTransfer(dev, I2C_SMBUS_WRITE, 0, I2C_SMBUS_QUICK, &data);
    
    return ret;
}

/**
 * @brief SMBus write byte
 * @param dev - device pointer
 * @param value - data
 * @return bI2cState_e - success, fail
*/
bI2cState_e bI2cSmbusWriteByte(bI2cDev_t *dev, const uint8_t value)
{
    if (!dev || dev->fd < 0) {
        return I2C_ERR_NOT_OPEN;
    }
    
    union i2c_smbus_data data;
    data.byte = value;
    
    bI2cState_e ret = smbusTransfer(dev, I2C_SMBUS_WRITE, 0, I2C_SMBUS_BYTE, &data);
    
    return ret;
}

/**
 * @brief SMBus read byte
 * @param dev  - device pointer
 * @param data - data pointer
 * @return bI2cState_e - success, fail
*/
bI2cState_e bI2cSmbusReadByte(bI2cDev_t *dev, uint8_t *value) {
    if (!dev || dev->fd < 0) {
        return I2C_ERR_NOT_OPEN;
    }
    
    if (!value) {
        return I2C_ERR_ARG;
    }
    
    union i2c_smbus_data data;
    bI2cState_e ret = smbusTransfer(dev, I2C_SMBUS_READ, 0, I2C_SMBUS_BYTE, &data);
    
    if (ret == I2C_SUCCESS) {
        *value = data.byte;
    }
    
    return ret;
}

/**
 * @brief SMBus write byte data
 * @param dev - device pointer
 * @param command - command
 * @param value - data
 * @return bI2cState_e - success, fail
*/
bI2cState_e bI2cSmbusWriteByteData(bI2cDev_t *dev, uint8_t command, uint8_t value) 
{
    if (!dev || dev->fd < 0) {
        return I2C_ERR_NOT_OPEN;
    }
    
    union i2c_smbus_data data;
    data.byte = value;
    
    bI2cState_e ret = smbusTransfer(dev, I2C_SMBUS_WRITE, command, I2C_SMBUS_BYTE_DATA, &data);
    
    return ret;
}

/**
 * @brief SMBus read byte data
 * @param dev - device pointer
 * @param command - command
 * @param value - data pointer
 * @return bI2cState_e - success, fail
*/
bI2cState_e bI2cSmbusReadByteData(bI2cDev_t *dev, uint8_t command, uint8_t *value) 
{
    if (!dev || dev->fd < 0) {
        return I2C_ERR_NOT_OPEN;
    }
    
    if (!value) {
        return I2C_ERR_ARG;
    }
    
    union i2c_smbus_data data;
    bI2cState_e ret = smbusTransfer(dev, I2C_SMBUS_READ, command, I2C_SMBUS_BYTE_DATA, &data);

    if (ret == I2C_SUCCESS) {
        *value = data.byte;
    }
    
    return ret;
}

/**
 * @brief SMBus写字数据
 * @param dev - 设备指针
 * @param command - 命令
 * @param value - 数据
 * @return I2C_SUCCESS - 成功， 其他 - 失败
*/
bI2cState_e bI2cSmbusWriteWordData(bI2cDev_t *dev, uint8_t command, uint16_t value) {
    if (!dev || dev->fd < 0) {
        return I2C_ERR_NOT_OPEN;
    }
    
    union i2c_smbus_data data;
    data.word = value;
    
    bI2cState_e ret = smbusTransfer(dev, I2C_SMBUS_WRITE, command, I2C_SMBUS_WORD_DATA, &data);
    
    return ret;
}

/**
 * @brief SMBus read word data
 * @param dev - device pointer
 * @param command - command
 * @param value - data pointer
 * @return bI2cState_e - success, fail
*/
bI2cState_e bI2cSmbusReadWordData(bI2cDev_t *dev, uint8_t command, uint16_t *value) 
{
    if (!dev || dev->fd < 0) {
        return I2C_ERR_NOT_OPEN;
    }
    
    if (!value) {
        return I2C_ERR_ARG;
    }
    
    union i2c_smbus_data data;
    bI2cState_e ret = smbusTransfer(dev, I2C_SMBUS_READ, command, I2C_SMBUS_WORD_DATA, &data);
    
    if (ret == I2C_SUCCESS) {
        *value = data.word;
    }
    
    return ret;
}

/**
 * @brief SMBus write block data
 * @param dev - device pointer
 * @param command - command
 * @param data - data pointer
 * @param length - data length
 * @return bI2cState_e - success, fail
*/
bI2cState_e bI2cSmbusWriteBlockData(bI2cDev_t *dev, uint8_t command, const uint8_t *data, uint8_t length) 
{
    if (!dev || dev->fd < 0) {
        return I2C_ERR_NOT_OPEN;
    }
    
    if (!data || length == 0 || length > I2C_SMBUS_BLOCK_MAX) {
        return I2C_ERR_ARG;
    }
    
    union i2c_smbus_data smbus_data;
    smbus_data.block[0] = length;
    memcpy(&smbus_data.block[1], data, length);
    
    bI2cState_e ret = smbusTransfer(dev, I2C_SMBUS_WRITE, command, I2C_SMBUS_BLOCK_DATA, &smbus_data);
    
    return ret;
}

/**
 * @brief SMBus read block data
 * @param dev - device pointer
 * @param command - command
 * @param data - data pointer
 * @param length - data length pointer
 * @return bI2cState_e - success, fail
*/
bI2cState_e bI2cSmbusReadBlockData(bI2cDev_t *dev, uint8_t command, uint8_t *data, uint8_t *length)
{
    if (!dev || dev->fd < 0) {
        return I2C_ERR_NOT_OPEN;
    }
    
    if (!data || !length) {
        return I2C_ERR_ARG;
    }
    
    union i2c_smbus_data smbus_data;
    bI2cState_e ret = smbusTransfer(dev, I2C_SMBUS_READ, command, I2C_SMBUS_BLOCK_DATA, &smbus_data);
    
    if (ret == I2C_SUCCESS) {
        uint8_t len = smbus_data.block[0];
        if (len > I2C_SMBUS_BLOCK_MAX) {
            return I2C_ERR_SMBUS;
        }
        
        if (len > *length) {
            return I2C_ERR_ARG;
        }
        
        memcpy(data, &smbus_data.block[1], len);
        *length = len;
    }
    
    return ret;
}

/**
 * @brief SMBus process call
 * @param dev - device pointer
 * @param command - command
 * @param wvalue - data
 * @param rvalue - data pointer
 * @return bI2cState_e - success, fail
*/
bI2cState_e bI2cSmbusProcessCall(bI2cDev_t *dev, uint8_t command, uint16_t wvalue, uint16_t *rvalue)
{
    if (!dev || dev->fd < 0) {
        return I2C_ERR_NOT_OPEN;
    }
    
    if (!rvalue) {
        return I2C_ERR_ARG;
    }
    
    union i2c_smbus_data data;
    data.word = wvalue;
    
    bI2cState_e ret = smbusTransfer(dev, I2C_SMBUS_WRITE, command, I2C_SMBUS_PROC_CALL, &data);
    
    if (ret == I2C_SUCCESS) {
        *rvalue = data.word;
    }
    
    return ret;
}

/**
 * @brief SMBus block process call
 * @param dev - device pointer
 * @param command - command
 * @param wdata - data pointer
 * @param wlength - data length
 * @param rdata - data pointer
 * @param rlength - data length pointer
 * @return bI2cState_e - success, fail
*/
bI2cState_e bI2cSmbusBlockProcessCall(bI2cDev_t *dev, uint8_t command, const uint8_t *wdata, uint8_t wlength, uint8_t *rdata, uint8_t *rlength)
{
    if (!dev || dev->fd < 0) {
        return I2C_ERR_NOT_OPEN;
    }
    
    if (!wdata || wlength == 0 || !rdata || !rlength) {
        return I2C_ERR_ARG;
    }
    
    if (wlength > I2C_SMBUS_BLOCK_MAX) {
        return I2C_ERR_ARG;
    }
    
    union i2c_smbus_data data;
    data.block[0] = wlength;
    memcpy(&data.block[1], wdata, wlength);
    
    bI2cState_e ret = smbusTransfer(dev, I2C_SMBUS_WRITE, command, I2C_SMBUS_BLOCK_PROC_CALL, &data);
    
    if (ret == I2C_SUCCESS) {
        uint8_t len = data.block[0];
        if (len > I2C_SMBUS_BLOCK_MAX) {
            return I2C_ERR_ARG;
        }
        
        if (len > *rlength) {
            return I2C_ERR_ARG;
        }
        
        memcpy(rdata, &data.block[1], len);
        *rlength = len;
    }
    
    return ret;
}

/**
 * @brief enable/disable SMBus PEC
 * @param dev - device pointer
 * @param enable - enable
 * @return bI2cState_e - success, fail
*/
bI2cState_e bI2cSmbusSetPec(bI2cDev_t *dev, const smbusPec_e enable)
{
    if (!dev || dev->fd < 0) {
        return I2C_ERR_NOT_OPEN;
    }
    
    int pec = enable ? 1 : 0;
    if (ioctl(dev->fd, I2C_PEC, pec) < 0) {
        return I2C_ERR_IOCTL;
    }
    
    return I2C_SUCCESS;
}

/**
 * @brief set SMBus timeout
 * @param dev - device pointer
 * @param timeout_ms - timeout time
 * @return bI2cState_e - success, fail
*/
bI2cState_e bI2cSmbusSetTimeout(bI2cDev_t *dev, int timeout_ms)
{
    if (!dev || dev->fd < 0) {
        return I2C_ERR_NOT_OPEN;
    }
    
    if (timeout_ms < 0) {
        return I2C_ERR_ARG;
    }
    
    if (ioctl(dev->fd, I2C_TIMEOUT, timeout_ms) < 0) {
        return I2C_ERR_IOCTL;
    }
    
    return I2C_SUCCESS;
}

/**
 * @brief set SMBus retries
 * @param dev - device pointer
 * @param retries - retries
 * @return bI2cState_e - success, fail
*/
bI2cState_e bI2cSmbusSetRetries(bI2cDev_t *dev, int retries)
{
    if (!dev || dev->fd < 0) {
        return I2C_ERR_NOT_OPEN;
    }
    
    if (retries < 0) {
        return I2C_ERR_ARG;
    }
    
    if (ioctl(dev->fd, I2C_RETRIES, retries) < 0) {
        return I2C_ERR_IOCTL;
    }

    return I2C_SUCCESS;
}
