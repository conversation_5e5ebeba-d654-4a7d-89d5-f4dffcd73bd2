/*******************************************************************************
* ZHT Communication & technology Co.Ltd 2019-2029 Copyright reserved
* File : bI2c.h
* Function : Base on linux i2c-dev, a useful i2c lib
* Author : zhenmingwei
* Created on: 2025.07.10
*
* Modification history:
* Ver Who Date Changes
* ------------------------------------------------------------------------------
* V1.0 zhenmingwei 2025/07/10 First release
********************************************************************************/
#ifndef __B_I2C_H__
#define __B_I2C_H__

#include <stdlib.h>
#include <string.h>
#include <stdint.h>
#include <unistd.h>
#include <assert.h>
#include <fcntl.h>
#include <sys/ioctl.h>
#include <linux/i2c.h>
#include <linux/i2c-dev.h>

// state code definition
typedef enum {
    I2C_SUCCESS = 0,
    I2C_ERR_OPEN = -1,
    I2C_ERR_IOCTL = -2,
    I2C_ERR_WRITE = -3,
    I2C_ERR_READ = -4,
    I2C_ERR_ARG = -5,
    I2C_ERR_SMBUS = -6,
    I2C_ERR_NOT_OPEN = -7,
    I2C_ERR_ADDR_SET = -8,
    I2C_ERR_TIMEOUT = -9,
    I2C_ERR_PEC = -10,
} bI2cState_e;

typedef enum {DIS_SMBUS_PEC, EN_SMBUS_PEC}smbusPec_e;

typedef struct bI2cDev bI2cDev_t;

/**
 * @brief open I2C device
 * @param devPath - I2C device path
 * @param devAddr - I2C slave device address
 * @return bI2cDev_t* - success, NULL - fail
*/
bI2cDev_t* bI2cOpen(const char* devPath, const uint8_t devAddr);

/**
 * @brief get I2C device file descriptor
 * @param dev - I2C device pointer
 * @return int - file descriptor
*/
int bI2cGetFd(bI2cDev_t* dev);

/**
 * @brief get I2C device path
 * @param dev - I2C device pointer
 * @return char* - device path
*/
const char* bI2cGetDevPath(bI2cDev_t* dev);

/**
 * @brief get I2C slave device address
 * @param dev - I2C device pointer
 * @return int - slave address
*/
int bI2cGetDevAddr(bI2cDev_t* dev);

/**
 * @brief close I2C device
 * @param dev - I2C device pointer
*/
void bI2cClose(bI2cDev_t* dev);

/**
 * @brief check I2C device function
 * @param dev   - device pointer
 * @param funcs - function pointer
 * @return bI2cState_e - success, fail
*/
bI2cState_e  bI2cCheckFuncs(bI2cDev_t* dev, uint32_t* funcs);

/**
 * @brief set I2C slave device address
 * @param dev  - device pointer
 * @param addr - slave device address
 * @return bI2cState_e - success, fail
*/
bI2cState_e bI2cSetSlaveAddr(bI2cDev_t* dev, const uint8_t addr);

// ========================
// I2C core operation
// ========================

/**
 * @brief I2C write operation
 * @param dev        - device pointer
 * @param data       - data pointer
 * @param len        - data length
 * @return bI2cState_e - success, fail
*/
bI2cState_e bI2cWrite(bI2cDev_t *dev, const uint8_t *data, size_t len);

/**
 * @brief I2C read operation
 * @param dev        - device pointer
 * @param data       - data pointer
 * @param len        - data length
 * @return bI2cState_e - success, fail
*/
bI2cState_e bI2cRead(bI2cDev_t *dev, uint8_t *data, size_t len);

/**
 * @brief I2C write and read operation
 * @param dev        - device pointer
 * @param wdata      - write data pointer
 * @param wlen       - write data length
 * @param rdata      - read data pointer
 * @param rlen       - read data length
 * @return bI2cState_e - success, fail
*/
bI2cState_e bI2cWriteRead(bI2cDev_t *dev, const uint8_t *wdata, size_t wlen, uint8_t *rdata, size_t rlen);

// ========================
// SMBus protocol operation
// ========================

/**
 * @brief SMBus quick command
 * @param dev - device pointer
 * @return bI2cState_e - success, fail
*/
bI2cState_e bI2cSmbusQuick(bI2cDev_t* dev);

/**
 * @brief SMBus write byte
 * @param dev - device pointer
 * @param value - data
 * @return bI2cState_e - success, fail
*/
bI2cState_e bI2cSmbusWriteByte(bI2cDev_t *dev, const uint8_t value);

/**
 * @brief SMBus read byte
 * @param dev  - device pointer
 * @param data - data pointer
 * @return bI2cState_e - success, fail
*/
bI2cState_e bI2cSmbusReadByte(bI2cDev_t* dev, uint8_t* value);

/**
 * @brief SMBus write byte data
 * @param dev - device pointer
 * @param command - command
 * @param value - data
 * @return bI2cState_e - success, fail
*/
bI2cState_e bI2cSmbusWriteByteData(bI2cDev_t *dev, uint8_t command, uint8_t value);

/**
 * @brief SMBus read byte data
 * @param dev - device pointer
 * @param command - command
 * @param value - data pointer
 * @return bI2cState_e - success, fail
*/
bI2cState_e bI2cSmbusReadByteData(bI2cDev_t *dev, uint8_t command, uint8_t *value);

/**
 * @brief SMBus read word data
 * @param dev - device pointer
 * @param command - command
 * @param value - data pointer
 * @return bI2cState_e - success, fail
*/
bI2cState_e bI2cSmbusReadWordData(bI2cDev_t *dev, uint8_t command, uint16_t *value);

/**
 * @brief SMBus write block data
 * @param dev - device pointer
 * @param command - command
 * @param data - data pointer
 * @param length - data length
 * @return bI2cState_e - success, fail
*/
bI2cState_e bI2cSmbusWriteBlockData(bI2cDev_t *dev, uint8_t command, const uint8_t *data, uint8_t length);

/**
 * @brief SMBus read block data
 * @param dev - device pointer
 * @param command - command
 * @param data - data pointer
 * @param length - data length pointer
 * @return bI2cState_e - success, fail
*/
bI2cState_e bI2cSmbusReadBlockData(bI2cDev_t *dev, uint8_t command, uint8_t *data, uint8_t *length);

/**
 * @brief SMBus process call
 * @param dev - device pointer
 * @param command - command
 * @param wvalue - data
 * @param rvalue - data pointer
 * @return bI2cState_e - success, fail
*/
bI2cState_e bI2cSmbusProcessCall(bI2cDev_t *dev, uint8_t command, uint16_t wvalue, uint16_t *rvalue);

/**
 * @brief SMBus block process call
 * @param dev - device pointer
 * @param command - command
 * @param wdata - data pointer
 * @param wlength - data length
 * @param rdata - data pointer
 * @param rlength - data length pointer
 * @return bI2cState_e - success, fail
*/
bI2cState_e bI2cSmbusBlockProcessCall(bI2cDev_t *dev, uint8_t command, const uint8_t *wdata, uint8_t wlength, uint8_t *rdata, uint8_t *rlength);

/**
 * @brief enable/disable SMBus PEC
 * @param dev - device pointer
 * @param enable - enable
 * @return bI2cState_e - success, fail
*/
bI2cState_e bI2cSmbusSetPec(bI2cDev_t *dev, const smbusPec_e enable);

/**
 * @brief set SMBus timeout
 * @param dev - device pointer
 * @param timeout_ms - timeout time
 * @return bI2cState_e - success, fail
*/
bI2cState_e bI2cSmbusSetTimeout(bI2cDev_t *dev, int timeout_ms);

/**
 * @brief set SMBus retries
 * @param dev - device pointer
 * @param retries - retries
 * @return bI2cState_e - success, fail
*/
bI2cState_e bI2cSmbusSetRetries(bI2cDev_t *dev, int retries);
#endif