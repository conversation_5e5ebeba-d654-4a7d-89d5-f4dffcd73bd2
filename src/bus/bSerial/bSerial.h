/*******************************************************************************
* ZHT Communication & technology Co.Ltd 2019-2029 Copyright reserved
* File : bSerial.h
* Function : Base on linux, a useful serial lib
* Author : yangzuogui
* Created on: 2025.07.16
*
* Modification history:
* Ver Who Date Changes
* ------------------------------------------------------------------------------
* V1.0 yangzuogui 2025/07/16 First release
********************************************************************************/
#ifndef __B_SERIAL_H__
#define __B_SERIAL_H__

#ifdef __cplusplus
extern "C" {
#endif


#include <stdint.h>
#include <stddef.h>
#include <stdbool.h>

/**
 * @brief Serial error codes
 */
typedef enum bSerialResult {
    bSerialResult_eSUCCESS              = 0,
    bSerialResult_eERROR_ARG            = -1, /* Invalid arguments */
    bSerialResult_eERROR_OPEN           = -2, /* Opening serial port */
    bSerialResult_eERROR_QUERY          = -3, /* Querying serial port attributes */
    bSerialResult_eERROR_CONFIGURE      = -4, /* Configuring serial port attributes */
    bSerialResult_eERROR_IO             = -5, /* Reading/writing serial port */
    bSerialResult_eERROR_CLOSE          = -6, /* Closing serial port */
} bSerialResult_e;

/**
 * @brief Serial parity options
 */
typedef enum bSerialParity {
    bSerialParity_eNONE,   /**< No parity */
    bSerialParity_eODD,    /**< Odd parity */
    bSerialParity_eEVEN,   /**< Even parity */
} bSerialParity_e;

/**
 * @brief Opaque serial handle structure
 */
typedef struct bSerialDev bSerialDev_t;

/**
 * @brief Allocate a new serial handle
 * @return Pointer to bSerialDev_t, or NULL if allocation fails
 */
bSerialDev_t *bSerialInit(const char *path, uint32_t baudrate, uint32_t databits, 
                            bSerialParity_e parity, uint32_t stopbits);

/**
 * @brief Free a serial handle
 * @param serial Serial handle pointer
 */
void bSerialRelease(bSerialDev_t *serial);

/**
 * @brief Read data from the serial port
 * @param serial Serial handle pointer
 * @param buf Buffer to store read data
 * @param len Maximum number of bytes to read
 * @param timeoutMs Timeout in milliseconds (-1 for infinite)
 * @return Number of bytes read, or negative error code
 */
int32_t bSerialRead(bSerialDev_t *serial, uint8_t *buf, size_t len, int32_t timeoutMs);

/**
 * @brief Write data to the serial port
 * @param serial Serial handle pointer
 * @param buf Buffer containing data to write
 * @param len Number of bytes to write
 * @return Number of bytes written, or negative error code
 */
int32_t bSerialWrite(bSerialDev_t *serial, const uint8_t *buf, size_t len);

/**
 * @brief Flush the serial port buffers
 * @param serial Serial handle pointer
 * @return 0 on success, negative error code on failure
 */
int32_t bSerialFlush(bSerialDev_t *serial);

/**
 * @brief Check if input data is waiting in the serial port
 * @param serial Serial handle pointer
 * @return Number of bytes
 */
int32_t bSerialGetInputQueueCnt(bSerialDev_t *serial);

/**
 * @brief Check if output data is waiting in the serial port
 * @param serial Serial handle pointer
 * @return Number of bytes
 */
int32_t bSerialGetOutputQueueCnt(bSerialDev_t *serial);

/**
 * @brief Poll the serial port for events
 * @param serial Serial handle pointer
 * @param timeoutMs Timeout in milliseconds (-1 for infinite)
 * @return 1 if data is available, 0 if timed out, negative error code on failure
 */
int32_t bIsSerialHasData(bSerialDev_t *serial, int32_t timeoutMs);

/**
 * @brief Get the current baudrate of the serial port
 * @param serial Serial handle pointer
 * @param baudrate Pointer to store the baudrate
 * @return 0 on success, negative error code on failure
 */
int32_t bSerialGetBaudrate(bSerialDev_t *serial, uint32_t *baudrate);

/**
 * @brief Get the current data bits of the serial port
 * @param serial Serial handle pointer
 * @param databits Pointer to store the data bits
 * @return 0 on success, negative error code on failure
 */
int32_t bSerialGetDatabits(bSerialDev_t *serial, uint32_t *databits);

/**
 * @brief Get the current parity of the serial port
 * @param serial Serial handle pointer
 * @param parity Pointer to store the parity
 * @return 0 on success, negative error code on failure
 */
int32_t bSerialGetParity(bSerialDev_t *serial, bSerialParity_e *parity);

/**
 * @brief Get the current stop bits of the serial port
 * @param serial Serial handle pointer
 * @param stopbits Pointer to store the stop bits
 * @return 0 on success, negative error code on failure
 */
int32_t bSerialGetStopbits(bSerialDev_t *serial, uint32_t *stopbits);

/**
 * @brief Get the current XON/XOFF flow control state
 * @param serial Serial handle pointer
 * @param xonxoff Pointer to store the state
 * @return 0 on success, negative error code on failure
 */
int32_t bSerialGetXonxoff(bSerialDev_t *serial, bool *xonxoff);

/**
 * @brief Get the current RTS/CTS flow control state
 * @param serial Serial handle pointer
 * @param rtscts Pointer to store the state
 * @return 0 on success, negative error code on failure
 */
int32_t bSerialGetRtscts(bSerialDev_t *serial, bool *rtscts);

/**
 * @brief Get the current VMIN value
 * @param serial Serial handle pointer
 * @param vmin Pointer to store the VMIN value
 * @return 0 on success, negative error code on failure
 */
int32_t bSerialGetVmin(bSerialDev_t *serial, uint32_t *vmin);

/**
 * @brief Get the current VTIME value
 * @param serial Serial handle pointer
 * @param vtime Pointer to store the VTIME value
 * @return 0 on success, negative error code on failure
 */
int32_t bSerialGetVtime(bSerialDev_t *serial, uint32_t *vtimeMs);

/**
 * @brief Set the baudrate of the serial port
 * @param serial Serial handle pointer
 * @param baudrate Baudrate to set
 * @return 0 on success, negative error code on failure
 */
int32_t bSerialSetBaudrate(bSerialDev_t *serial, uint32_t baudrate);

/**
 * @brief Set the data bits of the serial port
 * @param serial Serial handle pointer
 * @param databits Data bits to set (5, 6, 7, 8)
 * @return 0 on success, negative error code on failure
 */
int32_t bSerialSetDatabits(bSerialDev_t *serial, uint32_t databits);

/**
 * @brief Set the parity of the serial port
 * @param serial Serial handle pointer
 * @param parity Parity to set (PARITY_NONE, PARITY_ODD, PARITY_EVEN)
 * @return 0 on success, negative error code on failure
 */
int32_t bSerialSetParity(bSerialDev_t *serial, bSerialParity_e parity);

/**
 * @brief Set the stop bits of the serial port
 * @param serial Serial handle pointer
 * @param stopbits Stop bits to set (1, 2)
 * @return 0 on success, negative error code on failure
 */
int32_t bSerialSetStopbits(bSerialDev_t *serial, uint32_t stopbits);

/**
 * @brief Set the XON/XOFF flow control state
 * @param serial Serial handle pointer
 * @param enabled True to enable, false to disable
 * @return 0 on success, negative error code on failure
 */
int32_t bSerialSetXonxoff(bSerialDev_t *serial, bool enabled);

/**
 * @brief Set the RTS/CTS flow control state
 * @param serial Serial handle pointer
 * @param enabled True to enable, false to disable
 * @return 0 on success, negative error code on failure
 */
int32_t bSerialSetRtscts(bSerialDev_t *serial, bool enabled);

/**
 * @brief Set the VMIN value
 * @param serial Serial handle pointer
 * @param vmin VMIN value to set
 * @return 0 on success, negative error code on failure
 */
int32_t bSerialSetVmin(bSerialDev_t *serial, uint32_t vmin);

/**
 * @brief Set the VTIME value
 * @param serial Serial handle pointer
 * @param vtime VTIME value to set
 * @return 0 on success, negative error code on failure
 */
int32_t bSerialSetVtime(bSerialDev_t *serial, uint32_t vtimeMs);

/**
 * @brief Get the file descriptor of the serial handle
 * @param serial Serial handle pointer
 * @return File descriptor
 */
int32_t bSerialGetFd(bSerialDev_t *serial);

#ifdef __cplusplus
}
#endif

#endif