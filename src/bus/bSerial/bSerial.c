/*******************************************************************************
* ZHT Communication & technology Co.Ltd 2019-2029 Copyright reserved
* File : bSerial.h
* Function : Base on linux, a useful serial lib
* Author : yangzuogui
* Created on: 2025.07.16
*
* Modification history:
* Ver Who Date Changes
* ------------------------------------------------------------------------------
* V1.0 yangzuogui 2025/07/16 First release
********************************************************************************/
#include <stddef.h>
#include <stdint.h>
#include <stdbool.h>
#include <stdarg.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <errno.h>
#include <fcntl.h>
#include <poll.h>
#include <sys/select.h>
#include <sys/ioctl.h>
#include <termios.h>
#include "bSerial.h"

// #define _B_SERIAL_DEBUG
#ifdef _B_SERIAL_DEBUG
#define     B_SERIAL_DEBUG(...)          fprintf(stderr, "[BSERIAL] "__VA_ARGS__);
#define     B_SERIAL_INFO(...)           fprintf(stderr, "[BSERIAL] "__VA_ARGS__);
#define     B_SERIAL_ERROR(...)          fprintf(stderr, "[BSERIAL] "__VA_ARGS__);
#else
#define     B_SERIAL_DEBUG(...)
#define     B_SERIAL_INFO(...)
#define     B_SERIAL_ERROR(...)
#endif

/**
 * @brief Serial handle structure
 *        Contains file descriptor, error info, and termios timeout flag
 */
struct bSerialDev {
    int32_t fd;                     // Serial port file descriptor
    bool useTermiosTimeout;         // Use termios VMIN/VTIME for read timeout
};

/**
 * @brief Generic wrapper to get termios attributes
 */
static int32_t bSerialGetAttr(bSerialDev_t *ptSerialDev, struct termios *termiosSettings) 
{
    if (tcgetattr(ptSerialDev->fd, termiosSettings) < 0) {
        B_SERIAL_ERROR("Getting serial port attributes\n");
        return bSerialResult_eERROR_QUERY;
    }
    return bSerialResult_eSUCCESS;
}

/**
 * @brief Generic wrapper to set termios attributes
 */
static int32_t bSerialSetAttr(bSerialDev_t *ptSerialDev, struct termios *termiosSettings) 
{
    if (tcsetattr(ptSerialDev->fd, TCSANOW, termiosSettings) < 0) {
        B_SERIAL_ERROR("Setting serial port attributes\n");
        return bSerialResult_eERROR_CONFIGURE;
    }
    return bSerialResult_eSUCCESS;
}

/**
 * @brief Convert baudrate to termios bits
 * @param baudrate Baudrate value
 * @return Termios baudrate bits, or -1 if unsupported
 */
static int32_t bSerialBaudrateToBits(uint32_t baudrate)
{
    switch (baudrate) {
        case 50: 
            return B50;
        case 75: 
            return B75;
        case 110: 
            return B110;
        case 134: 
            return B134;
        case 150: 
            return B150;
        case 200: 
            return B200;
        case 300: 
            return B300;
        case 600: 
            return B600;
        case 1200: 
            return B1200;
        case 1800: 
            return B1800;
        case 2400: 
            return B2400;
        case 4800: 
            return B4800;
        case 9600: 
            return B9600;
        case 19200: 
            return B19200;
        case 38400: 
            return B38400;
        case 57600: 
            return B57600;
        case 115200: 
            return B115200;
        case 230400: 
            return B230400;
        case 460800: 
            return B460800;
        case 500000: 
            return B500000;
        case 576000: 
            return B576000;
        case 921600: 
            return B921600;
        case 1000000: 
            return B1000000;
        case 1152000: 
            return B1152000;
        case 1500000: 
            return B1500000;
        case 2000000: 
            return ********;
#ifdef ********
        case 2500000: 
            return ********;
#endif
#ifdef ********
        case 3000000: 
            return ********;
#endif
#ifdef ********
        case 3500000: 
            return ********;
#endif
#ifdef ********
        case 4000000: 
            return ********;
#endif
        default: 
            return -1;
    }
}

/**
 * @brief Convert termios bits to baudrate
 * @param bits Termios baudrate bits
 * @return Baudrate value, or -1 if unsupported
 */
static int32_t bSerialBitsToBaudrate(uint32_t bits)
{
    switch (bits) {
        case B0: 
            return 0;
        case B50: 
            return 50;
        case B75: 
            return 75;
        case B110: 
            return 110;
        case B134: 
            return 134;
        case B150: 
            return 150;
        case B200: 
            return 200;
        case B300: 
            return 300;
        case B600: 
            return 600;
        case B1200: 
            return 1200;
        case B1800: 
            return 1800;
        case B2400: 
            return 2400;
        case B4800: 
            return 4800;
        case B9600: 
            return 9600;
        case B19200: 
            return 19200;
        case B38400: 
            return 38400;
        case B57600: 
            return 57600;
        case B115200: 
            return 115200;
        case B230400: 
            return 230400;
        case B460800: 
            return 460800;
        case B500000: 
            return 500000;
        case B576000: 
            return 576000;
        case B921600: 
            return 921600;
        case B1000000: 
            return 1000000;
        case B1152000: 
            return 1152000;
        case B1500000: 
            return 1500000;
        case ********: 
            return 2000000;
#ifdef ********
        case ********: 
            return 2500000;
#endif
#ifdef ********
        case ********: 
            return 3000000;
#endif
#ifdef ********
        case ********: 
            return 3500000;
#endif
#ifdef ********
        case ********: 
            return 4000000;
#endif
        default: 
            return bSerialResult_eERROR_ARG;
    }
}


/**
 * @brief Open a serial port with advanced settings
 * @param serial Serial handle pointer
 * @param path Serial port path (e.g., "/dev/ttyUSB0")
 * @param baudrate Baudrate
 * @param databits Data bits (5, 6, 7, 8)
 * @param parity Parity (PARITY_NONE, PARITY_ODD, PARITY_EVEN)
 * @param stopbits Stop bits (1, 2)
 * @param xonxoff Software flow control (true/false)
 * @param rtscts Hardware flow control (true/false)
 * @return 0 on success, negative error code on failure
 */
static int32_t bSerialOpen(bSerialDev_t *ptSerialDev, const char *path, uint32_t baudrate, uint32_t databits, bSerialParity_e parity, uint32_t stopbits, bool xonxoff, bool rtscts)
{
    struct termios termiosSettings;

    /* Validate args */
    if (databits != 5 && databits != 6 && databits != 7 && databits != 8) {
        B_SERIAL_ERROR("Invalid data bits (can be 5,6,7,8)\n");
        return bSerialResult_eERROR_ARG;
    }
    if (parity != bSerialParity_eNONE && parity != bSerialParity_eODD && parity != bSerialParity_eEVEN) {
        B_SERIAL_ERROR("Invalid parity (can be NONE,ODD,EVEN)\n");
        return bSerialResult_eERROR_ARG;
    }
    if (stopbits != 1 && stopbits != 2) {
        B_SERIAL_ERROR("Invalid stop bits (can be 1,2)\n");
        return bSerialResult_eERROR_ARG;
    }

    memset(ptSerialDev, 0, sizeof(bSerialDev_t));

    /* Open serial port */
    if ((ptSerialDev->fd = open(path, O_RDWR | O_NOCTTY)) < 0) {
        B_SERIAL_ERROR("Opening serial port \"%s\"\n", path);
        return bSerialResult_eERROR_OPEN;
    }

    memset(&termiosSettings, 0, sizeof(termiosSettings));

    /* c_iflag */

    /* Ignore break characters */
    termiosSettings.c_iflag = IGNBRK;
    if (parity != bSerialParity_eNONE) {
        termiosSettings.c_iflag |= INPCK;
    }
    /* Only use ISTRIP when less than 8 bits as it strips the 8th bit */
    if (parity != bSerialParity_eNONE && databits != 8) {
        termiosSettings.c_iflag |= ISTRIP;
    }
    if (xonxoff) {
        termiosSettings.c_iflag |= (IXON | IXOFF);
    }

    /* c_oflag */
    termiosSettings.c_oflag = 0;

    /* c_lflag */
    termiosSettings.c_lflag = 0;

    /* c_cflag */
    /* Enable receiver, ignore modem control lines */
    termiosSettings.c_cflag = CREAD | CLOCAL;

    /* Databits */
    if (databits == 5) {
        termiosSettings.c_cflag |= CS5;
    } else if (databits == 6) {
        termiosSettings.c_cflag |= CS6;
    } else if (databits == 7) {
        termiosSettings.c_cflag |= CS7;
    } else if (databits == 8) {
        termiosSettings.c_cflag |= CS8;
    }

    /* Parity */
    if (parity == bSerialParity_eEVEN) {
        termiosSettings.c_cflag |= PARENB;
    } else if (parity == bSerialParity_eODD) {
        termiosSettings.c_cflag |= (PARENB | PARODD);
    }

    /* Stopbits */
    if (stopbits == 2) {
        termiosSettings.c_cflag |= CSTOPB;
    }

    /* RTS/CTS */
#ifdef CRTSCTS
    if (rtscts) {
        termiosSettings.c_cflag |= CRTSCTS;
    }
#endif

    /* Baudrate */
    cfsetispeed(&termiosSettings, bSerialBaudrateToBits(baudrate));
    cfsetospeed(&termiosSettings, bSerialBaudrateToBits(baudrate));

    /* Set termios attributes */
    if (tcsetattr(ptSerialDev->fd, TCSANOW, &termiosSettings) < 0) {
        close(ptSerialDev->fd);
        ptSerialDev->fd = -1;
        B_SERIAL_ERROR("Setting serial port attributes\n");
        return bSerialResult_eERROR_CONFIGURE;
    }

    /* Not Use termios timeout default */
    ptSerialDev->useTermiosTimeout = false;

    return bSerialResult_eSUCCESS;
}

/**
 * @brief Close the serial port
 * @param serial Serial handle pointer
 * @return 0 on success, negative error code on failure
 */
static int32_t bSerialClose(bSerialDev_t *ptSerialDev)
{
    if (ptSerialDev->fd < 0) {
        return bSerialResult_eSUCCESS;
    }
    if (close(ptSerialDev->fd) < 0) {
        B_SERIAL_ERROR("Closing serial port\n");
        return bSerialResult_eERROR_CLOSE;
    }
    ptSerialDev->fd = -1;
    return bSerialResult_eSUCCESS;
}


/**
 * @brief Allocate a new serial handle
 * @return Pointer to bSerialDev_t, or NULL if allocation fails
 */
bSerialDev_t *bSerialInit(const char *path, uint32_t baudrate, uint32_t databits, bSerialParity_e parity, uint32_t stopbits)
{
    bSerialDev_t *ptSerialDev = calloc(1, sizeof(bSerialDev_t));
    if (ptSerialDev == NULL) {
        B_SERIAL_ERROR("Failed to allocate serial handle\n");
        return NULL;
    }
    int32_t ret = bSerialOpen(ptSerialDev, path, baudrate, databits, parity, stopbits, false, false);
    if (ret != 0) {
        B_SERIAL_ERROR("Failed to open serial port\n");
        free(ptSerialDev);
        return NULL;
    }
    return ptSerialDev;
}

/**
 * @brief Free a serial handle
 * @param serial Serial handle pointer
 */
void bSerialRelease(bSerialDev_t *ptSerialDev)
{
    if (ptSerialDev) {
        bSerialClose(ptSerialDev);
        free(ptSerialDev);
    }
}



/**
 * @brief Read data from the serial port
 * @param serial Serial handle pointer
 * @param buf Buffer to store read data
 * @param len Maximum number of bytes to read
 * @param timeoutMs Timeout in milliseconds (-1 for infinite)
 * @return Number of bytes read, or negative error code
 */
int32_t bSerialRead(bSerialDev_t *ptSerialDev, uint8_t *buf, size_t len, int32_t timeoutMs)
{
    if (ptSerialDev == NULL) {
        B_SERIAL_ERROR("Serial port not initialized\n");
        return bSerialResult_eERROR_ARG;
    }
    if (buf == NULL) {
        B_SERIAL_ERROR("Buffer is NULL\n");
        return bSerialResult_eERROR_ARG;
    }
    if (len == 0) {
        B_SERIAL_ERROR("Length is 0\n");
        return bSerialResult_eERROR_ARG;
    }
    if (ptSerialDev->fd < 0) {
        B_SERIAL_ERROR("Serial port not open\n");
        return bSerialResult_eERROR_OPEN;
    }

    ssize_t ret;
    struct timeval tvTimeout;
    tvTimeout.tv_sec = timeoutMs / 1000;
    tvTimeout.tv_usec = (timeoutMs % 1000) * 1000;
    size_t bytesRead = 0;
    while (bytesRead < len) {
        fd_set rfds;
        FD_ZERO(&rfds);
        FD_SET(ptSerialDev->fd, &rfds);
        if ((ret = select(ptSerialDev->fd+1, &rfds, NULL, NULL, (timeoutMs < 0) ? NULL : &tvTimeout)) < 0) {
            B_SERIAL_ERROR("select() on serial port\n");
            return bSerialResult_eERROR_IO;
        }
        /* Timeout */
        if (ret == 0) {
            break;
        }
        if ((ret = read(ptSerialDev->fd, buf + bytesRead, len - bytesRead)) < 0) {
            B_SERIAL_ERROR("Reading serial port\n");
            return bSerialResult_eERROR_IO;
        }
        /* If we're using VMIN or VMIN+VTIME semantics for end of read, return now */
        if (ptSerialDev->useTermiosTimeout) {
            return ret;
        }
        /* Empty read */
        if (ret == 0 && len != 0) {
            B_SERIAL_ERROR("Reading serial port: unexpected empty read\n");
            return bSerialResult_eERROR_IO;
        }
        bytesRead += ret;
    }
    return bytesRead;
}

/**
 * @brief Write data to the serial port
 * @param serial Serial handle pointer
 * @param buf Buffer containing data to write
 * @param len Number of bytes to write
 * @return Number of bytes written, or negative error code
 */
int32_t bSerialWrite(bSerialDev_t *ptSerialDev, const uint8_t *buf, size_t len)
{
    if (ptSerialDev == NULL) {
        B_SERIAL_ERROR("Serial port not initialized\n");
        return bSerialResult_eERROR_ARG;
    }
    if (buf == NULL) {
        B_SERIAL_ERROR("Buffer is NULL\n");
        return bSerialResult_eERROR_ARG;
    }
    if (len == 0) {
        B_SERIAL_ERROR("Length is 0\n");
        return bSerialResult_eERROR_ARG;
    }
    if (ptSerialDev->fd < 0) {
        B_SERIAL_ERROR("Serial port not open\n");
        return bSerialResult_eERROR_OPEN;
    }
    
    ssize_t ret;
    if ((ret = write(ptSerialDev->fd, buf, len)) < 0) {
        B_SERIAL_ERROR("Writing serial port\n");
        return bSerialResult_eERROR_IO;
    }
    return ret;
}

/**
 * @brief Flush the serial port buffers
 * @param serial Serial handle pointer
 * @return 0 on success, negative error code on failure
 */
int32_t bSerialFlush(bSerialDev_t *ptSerialDev)
{
    if (ptSerialDev == NULL) {
        B_SERIAL_ERROR("Serial port not initialized\n");
        return bSerialResult_eERROR_ARG;
    }
    if (ptSerialDev->fd < 0) {
        B_SERIAL_ERROR("Serial port not open\n");
        return bSerialResult_eERROR_OPEN;
    }
    if (tcdrain(ptSerialDev->fd) < 0) {
        B_SERIAL_ERROR("Flushing serial port\n");
        return bSerialResult_eERROR_IO;
    }
    return bSerialResult_eSUCCESS;
}

/**
 * @brief Check if input data is waiting in the serial port
 * @param serial Serial handle pointer
 * @return 0 on success, negative error code on failure
 */
int32_t bSerialGetInputQueueCnt(bSerialDev_t *ptSerialDev)
{
    if (ptSerialDev == NULL) {
        B_SERIAL_ERROR("Serial port not initialized\n");
        return bSerialResult_eERROR_ARG;
    }
    if (ptSerialDev->fd < 0) {
        B_SERIAL_ERROR("Serial port not open\n");
        return bSerialResult_eERROR_OPEN;
    }
    uint32_t count = 0;
    if (ioctl(ptSerialDev->fd, TIOCINQ, &count) < 0) {
        B_SERIAL_ERROR("TIOCINQ query\n");
        return bSerialResult_eERROR_IO;
    }
    return (int32_t)count;
}

/**
 * @brief Check if output data is waiting in the serial port
 * @param serial Serial handle pointer
 * @return 0 on success, negative error code on failure
 */
int32_t bSerialGetOutputQueueCnt(bSerialDev_t *ptSerialDev)
{
    if (ptSerialDev == NULL) {
        B_SERIAL_ERROR("Serial port not initialized\n");
        return bSerialResult_eERROR_ARG;
    }
    if (ptSerialDev->fd < 0) {
        B_SERIAL_ERROR("Serial port not open\n");
        return bSerialResult_eERROR_OPEN;
    }
    uint32_t count = 0;
    if (ioctl(ptSerialDev->fd, TIOCOUTQ, &count) < 0) {
        B_SERIAL_ERROR("TIOCOUTQ query\n");
        return bSerialResult_eERROR_IO;
    }
    return (int32_t)count;
}

/**
 * @brief Poll the serial port for events
 * @param serial Serial handle pointer
 * @param timeoutMs Timeout in milliseconds (-1 for infinite)
 * @return 1 if data is available, 0 if timed out, negative error code on failure
 */
int32_t bIsSerialHasData(bSerialDev_t *ptSerialDev, int32_t timeoutMs)
{
    if (ptSerialDev == NULL) {
        B_SERIAL_ERROR("Serial port not initialized\n");
        return bSerialResult_eERROR_ARG;
    }
    if (ptSerialDev->fd < 0) {
        B_SERIAL_ERROR("Serial port not open\n");
        return bSerialResult_eERROR_OPEN;
    }
    struct pollfd fds[1];
    int32_t ret;
    /* Poll */
    fds[0].fd = ptSerialDev->fd;
    fds[0].events = POLLIN | POLLPRI;
    if ((ret = poll(fds, 1, timeoutMs)) < 0) {
        B_SERIAL_ERROR("Polling serial port\n");
        return bSerialResult_eERROR_IO;
    }
    if (ret) {
        return 1;
    }
    /* Timed out */
    return 0;
}


/**
 * @brief Get the current baudrate of the serial port
 * @param serial Serial handle pointer
 * @param baudrate Pointer to store the baudrate
 * @return 0 on success, negative error code on failure
 */
int32_t bSerialGetBaudrate(bSerialDev_t *ptSerialDev, uint32_t *baudrate)
{
    if (ptSerialDev == NULL) {
        B_SERIAL_ERROR("Serial port not initialized\n");
        return bSerialResult_eERROR_ARG;
    }
    if (ptSerialDev->fd < 0) {
        B_SERIAL_ERROR("Serial port not open\n");
        return bSerialResult_eERROR_OPEN;
    }
    struct termios termiosSettings;
    int32_t ret = bSerialGetAttr(ptSerialDev, &termiosSettings);
    if (ret < 0) {
        return ret;
    }
    *baudrate = bSerialBitsToBaudrate(cfgetospeed(&termiosSettings));
    return bSerialResult_eSUCCESS;
}

/**
 * @brief Get the current data bits of the serial port
 * @param serial Serial handle pointer
 * @param databits Pointer to store the data bits
 * @return 0 on success, negative error code on failure
 */
int32_t bSerialGetDatabits(bSerialDev_t *ptSerialDev, uint32_t *databits)
{
    if (ptSerialDev == NULL) {
        B_SERIAL_ERROR("Serial port not initialized\n");
        return bSerialResult_eERROR_ARG;
    }
    if (ptSerialDev->fd < 0) {
        B_SERIAL_ERROR("Serial port not open\n");
        return bSerialResult_eERROR_OPEN;
    }
    struct termios termiosSettings;
    int32_t ret = bSerialGetAttr(ptSerialDev, &termiosSettings);
    if (ret < 0) {
        return ret;
    }
    switch (termiosSettings.c_cflag & CSIZE) {
        case CS5:
            *databits = 5;
            break;
        case CS6:
            *databits = 6;
            break;
        case CS7:
            *databits = 7;
            break;
        case CS8:
            *databits = 8;
            break;
    }
    return bSerialResult_eSUCCESS;
}

/**
 * @brief Get the current parity of the serial port
 * @param serial Serial handle pointer
 * @param parity Pointer to store the parity
 * @return 0 on success, negative error code on failure
 */
int32_t bSerialGetParity(bSerialDev_t *ptSerialDev, bSerialParity_e *parity)
{
    if (ptSerialDev == NULL) {
        B_SERIAL_ERROR("Serial port not initialized\n");
        return bSerialResult_eERROR_ARG;
    }
    if (ptSerialDev->fd < 0) {
        B_SERIAL_ERROR("Serial port not open\n");
        return bSerialResult_eERROR_OPEN;
    }
    struct termios termiosSettings;
    int32_t ret = bSerialGetAttr(ptSerialDev, &termiosSettings);
    if (ret < 0) {
        return ret;
    }
    if ((termiosSettings.c_cflag & PARENB) == 0) {
        *parity = bSerialParity_eNONE;
    } else if ((termiosSettings.c_cflag & PARODD) == 0) {
        *parity = bSerialParity_eEVEN;
    } else {
        *parity = bSerialParity_eODD;
    }
    return bSerialResult_eSUCCESS;
}

/**
 * @brief Get the current stop bits of the serial port
 * @param serial Serial handle pointer
 * @param stopbits Pointer to store the stop bits
 * @return 0 on success, negative error code on failure
 */
int32_t bSerialGetStopbits(bSerialDev_t *ptSerialDev, uint32_t *stopbits)
{
    if (ptSerialDev == NULL) {
        B_SERIAL_ERROR("Serial port not initialized\n");
        return bSerialResult_eERROR_ARG;
    }
    if (ptSerialDev->fd < 0) {
        B_SERIAL_ERROR("Serial port not open\n");
        return bSerialResult_eERROR_OPEN;
    }
    struct termios termiosSettings;
    int32_t ret = bSerialGetAttr(ptSerialDev, &termiosSettings);
    if (ret < 0) {
        return ret;
    }
    if (termiosSettings.c_cflag & CSTOPB) {
        *stopbits = 2;
    } else {
        *stopbits = 1;
    }
    return bSerialResult_eSUCCESS;
}

/**
 * @brief Get the current XON/XOFF flow control state
 * @param serial Serial handle pointer
 * @param xonxoff Pointer to store the state
 * @return 0 on success, negative error code on failure
 */
int32_t bSerialGetXonxoff(bSerialDev_t *ptSerialDev, bool *xonxoff)
{
    if (ptSerialDev == NULL) {
        B_SERIAL_ERROR("Serial port not initialized\n");
        return bSerialResult_eERROR_ARG;
    }
    if (ptSerialDev->fd < 0) {
        B_SERIAL_ERROR("Serial port not open\n");
        return bSerialResult_eERROR_OPEN;
    }
    struct termios termiosSettings;
    int32_t ret = bSerialGetAttr(ptSerialDev, &termiosSettings);
    if (ret < 0) {
        return ret;
    }
    if (termiosSettings.c_iflag & (IXON | IXOFF)) {
        *xonxoff = true;
    } else {
        *xonxoff = false;
    }
    return bSerialResult_eSUCCESS;
}

/**
 * @brief Get the current RTS/CTS flow control state
 * @param serial Serial handle pointer
 * @param rtscts Pointer to store the state
 * @return 0 on success, negative error code on failure
 */
int32_t bSerialGetRtscts(bSerialDev_t *ptSerialDev, bool *rtscts)
{
    if (ptSerialDev == NULL) {
        B_SERIAL_ERROR("Serial port not initialized\n");
        return bSerialResult_eERROR_ARG;
    }
    if (ptSerialDev->fd < 0) {
        B_SERIAL_ERROR("Serial port not open\n");
        return bSerialResult_eERROR_OPEN;
    }
    struct termios termiosSettings;
    int32_t ret = bSerialGetAttr(ptSerialDev, &termiosSettings);
    if (ret < 0) {
        return ret;
    }
#ifdef CRTSCTS
    if (termiosSettings.c_cflag & CRTSCTS) {
        *rtscts = true;
    } else {
        *rtscts = false;
    }
#else
    *rtscts = false;
#endif
    return bSerialResult_eSUCCESS;
}

/**
 * @brief Get the current VMIN value
 * @param serial Serial handle pointer
 * @param vmin Pointer to store the VMIN value
 * @return 0 on success, negative error code on failure
 */
int32_t bSerialGetVmin(bSerialDev_t *ptSerialDev, uint32_t *vmin)
{
    if (ptSerialDev == NULL) {
        B_SERIAL_ERROR("Serial port not initialized\n");
        return bSerialResult_eERROR_ARG;
    }
    if (ptSerialDev->fd < 0) {
        B_SERIAL_ERROR("Serial port not open\n");
        return bSerialResult_eERROR_OPEN;
    }
    struct termios termiosSettings;
    int32_t ret = bSerialGetAttr(ptSerialDev, &termiosSettings);
    if (ret < 0) {
        return ret;
    }
    *vmin = termiosSettings.c_cc[VMIN];
    return bSerialResult_eSUCCESS;
}

/**
 * @brief Get the current VTIME value
 * @param serial Serial handle pointer
 * @param vtime Pointer to store the VTIME value
 * @return 0 on success, negative error code on failure
 */
int32_t bSerialGetVtime(bSerialDev_t *ptSerialDev, uint32_t *vtimeMs)
{
    if (ptSerialDev == NULL) {
        B_SERIAL_ERROR("Serial port not initialized\n");
        return bSerialResult_eERROR_ARG;
    }
    if (ptSerialDev->fd < 0) {
        B_SERIAL_ERROR("Serial port not open\n");
        return bSerialResult_eERROR_OPEN;
    }
    struct termios termiosSettings;
    int32_t ret = bSerialGetAttr(ptSerialDev, &termiosSettings);
    if (ret < 0) {
        return ret;
    }
    if (vtimeMs) {
        *vtimeMs = ((uint32_t)termiosSettings.c_cc[VTIME]) * 100; // 0.1秒单位转ms
    }
    return bSerialResult_eSUCCESS;
}

/**
 * @brief Set the baudrate of the serial port
 * @param serial Serial handle pointer
 * @param baudrate Baudrate to set
 * @return 0 on success, negative error code on failure
 */
int32_t bSerialSetBaudrate(bSerialDev_t *ptSerialDev, uint32_t baudrate)
{
    if (ptSerialDev == NULL) {
        B_SERIAL_ERROR("Serial port not initialized\n");
        return bSerialResult_eERROR_ARG;
    }
    if (ptSerialDev->fd < 0) {
        B_SERIAL_ERROR("Serial port not open\n");
        return bSerialResult_eERROR_OPEN;
    }
    struct termios termiosSettings;
    int32_t ret = bSerialGetAttr(ptSerialDev, &termiosSettings);
    if (ret < 0) {
        return ret;
    }
    cfsetispeed(&termiosSettings, bSerialBaudrateToBits(baudrate));
    cfsetospeed(&termiosSettings, bSerialBaudrateToBits(baudrate));
    return bSerialSetAttr(ptSerialDev, &termiosSettings);
}

/**
 * @brief Set the data bits of the serial port
 * @param serial Serial handle pointer
 * @param databits Data bits to set (5, 6, 7, 8)
 * @return 0 on success, negative error code on failure
 */
int32_t bSerialSetDatabits(bSerialDev_t *ptSerialDev, uint32_t databits)
{
    if (ptSerialDev == NULL) {
        B_SERIAL_ERROR("Serial port not initialized\n");
        return bSerialResult_eERROR_ARG;
    }
    if (ptSerialDev->fd < 0) {
        B_SERIAL_ERROR("Serial port not open\n");
        return bSerialResult_eERROR_OPEN;
    }
    struct termios termiosSettings;
    int32_t ret = bSerialGetAttr(ptSerialDev, &termiosSettings);
    if (ret < 0) {
        return ret;
    }
    termiosSettings.c_cflag &= ~CSIZE;
    if (databits == 5) {
        termiosSettings.c_cflag |= CS5;
    } else if (databits == 6) {
        termiosSettings.c_cflag |= CS6;
    } else if (databits == 7) {
        termiosSettings.c_cflag |= CS7;
    } else if (databits == 8) {
        termiosSettings.c_cflag |= CS8;
    }
    return bSerialSetAttr(ptSerialDev, &termiosSettings);
}

/**
 * @brief Set the parity of the serial port
 * @param serial Serial handle pointer
 * @param parity Parity to set (NONE, ODD, EVEN)
 * @return 0 on success, negative error code on failure
 */
int32_t bSerialSetParity(bSerialDev_t *ptSerialDev, bSerialParity_e parity)
{
    if (ptSerialDev == NULL) {
        B_SERIAL_ERROR("Serial port not initialized\n");
        return bSerialResult_eERROR_ARG;
    }
    if (ptSerialDev->fd < 0) {
        B_SERIAL_ERROR("Serial port not open\n");
        return bSerialResult_eERROR_OPEN;
    }
    struct termios termiosSettings;
    int32_t ret = bSerialGetAttr(ptSerialDev, &termiosSettings);
    if (ret < 0) {
        return ret;
    }
    termiosSettings.c_iflag &= ~(INPCK | ISTRIP);
    if (parity != bSerialParity_eNONE) {
        termiosSettings.c_iflag |= (INPCK | ISTRIP);
    }
    termiosSettings.c_cflag &= ~(PARENB | PARODD);
    if (parity == bSerialParity_eEVEN) {
        termiosSettings.c_cflag |= PARENB;
    } else if (parity == bSerialParity_eODD) {
        termiosSettings.c_cflag |= (PARENB | PARODD);
    }
    return bSerialSetAttr(ptSerialDev, &termiosSettings);
}

/**
 * @brief Set the stop bits of the serial port
 * @param serial Serial handle pointer
 * @param stopbits Stop bits to set (1, 2)
 * @return 0 on success, negative error code on failure
 */
int32_t bSerialSetStopbits(bSerialDev_t *ptSerialDev, uint32_t stopbits)
{
    if (ptSerialDev == NULL) {
        B_SERIAL_ERROR("Serial port not initialized\n");
        return bSerialResult_eERROR_ARG;
    }
    if (ptSerialDev->fd < 0) {
        B_SERIAL_ERROR("Serial port not open\n");
        return bSerialResult_eERROR_OPEN;
    }
    struct termios termiosSettings;
    int32_t ret = bSerialGetAttr(ptSerialDev, &termiosSettings);
    if (ret < 0) {
        return ret;
    }
    termiosSettings.c_cflag &= ~(CSTOPB);
    if (stopbits == 2) {
        termiosSettings.c_cflag |= CSTOPB;
    }
    return bSerialSetAttr(ptSerialDev, &termiosSettings);
}

/**
 * @brief Set the XON/XOFF flow control state
 * @param serial Serial handle pointer
 * @param enabled True to enable, false to disable
 * @return 0 on success, negative error code on failure
 */
int32_t bSerialSetXonxoff(bSerialDev_t *ptSerialDev, bool enabled)
{
    if (ptSerialDev == NULL) {
        B_SERIAL_ERROR("Serial port not initialized\n");
        return bSerialResult_eERROR_ARG;
    }
    if (ptSerialDev->fd < 0) {
        B_SERIAL_ERROR("Serial port not open\n");
        return bSerialResult_eERROR_OPEN;
    }
    struct termios termiosSettings;
    int32_t ret = bSerialGetAttr(ptSerialDev, &termiosSettings);
    if (ret < 0) {
        return ret;
    }
    termiosSettings.c_iflag &= ~(IXON | IXOFF | IXANY);
    if (enabled) {
        termiosSettings.c_iflag |= (IXON | IXOFF);
    }
    return bSerialSetAttr(ptSerialDev, &termiosSettings);
}

/**
 * @brief Set the RTS/CTS flow control state
 * @param serial Serial handle pointer
 * @param enabled True to enable, false to disable
 * @return 0 on success, negative error code on failure
 */
int32_t bSerialSetRtscts(bSerialDev_t *ptSerialDev, bool enabled)
{
    if (ptSerialDev == NULL) {
        B_SERIAL_ERROR("Serial port not initialized\n");
        return bSerialResult_eERROR_ARG;
    }
    if (ptSerialDev->fd < 0) {
        B_SERIAL_ERROR("Serial port not open\n");
        return bSerialResult_eERROR_OPEN;
    }
    struct termios termiosSettings;
    int32_t ret = bSerialGetAttr(ptSerialDev, &termiosSettings);
    if (ret < 0) {
        return ret;
    }
#ifdef CRTSCTS
    termiosSettings.c_cflag &= ~CRTSCTS;
    if (enabled) {
        termiosSettings.c_cflag |= CRTSCTS;
    }
#endif
    return bSerialSetAttr(ptSerialDev, &termiosSettings);
}

/**
 * @brief Set the VMIN value
 * @param serial Serial handle pointer
 * @param vmin VMIN value to set
 * @return 0 on success, negative error code on failure
 */
int32_t bSerialSetVmin(bSerialDev_t *ptSerialDev, uint32_t vmin)
{
    if (ptSerialDev == NULL) {
        B_SERIAL_ERROR("Serial port not initialized\n");
        return bSerialResult_eERROR_ARG;
    }
    if (ptSerialDev->fd < 0) {
        B_SERIAL_ERROR("Serial port not open\n");
        return bSerialResult_eERROR_OPEN;
    }
    struct termios termiosSettings;
    int32_t ret = bSerialGetAttr(ptSerialDev, &termiosSettings);
    if (ret < 0) {
        return ret;
    }
    termiosSettings.c_cc[VMIN] = vmin;
    ret = bSerialSetAttr(ptSerialDev, &termiosSettings);
    if (ret == 0) {
        ptSerialDev->useTermiosTimeout = vmin > 0 ? true : false;
    }
    return ret;
}

/**
 * @brief Set the VTIME value
 * @param serial Serial handle pointer
 * @param vtime VTIME value to set
 * @return 0 on success, negative error code on failure
 */
int32_t bSerialSetVtime(bSerialDev_t *ptSerialDev, uint32_t vtimeMs)
{
    if (ptSerialDev == NULL) {
        B_SERIAL_ERROR("Serial port not initialized\n");
        return bSerialResult_eERROR_ARG;
    }
    if (ptSerialDev->fd < 0) {
        B_SERIAL_ERROR("Serial port not open\n");
        return bSerialResult_eERROR_OPEN;
    }
    if (vtimeMs > 25500) {
        B_SERIAL_ERROR("Invalid vtimeMs (can be 0-25500)\n");
        return bSerialResult_eERROR_ARG;
    }
    struct termios termiosSettings;
    int32_t ret = bSerialGetAttr(ptSerialDev, &termiosSettings);
    if (ret < 0) {
        return ret;
    }
    termiosSettings.c_cc[VTIME] = vtimeMs / 100; // 0.1秒为单位
    return bSerialSetAttr(ptSerialDev, &termiosSettings);
}

/**
 * @brief Get the file descriptor of the serial handle
 * @param serial Serial handle pointer
 * @return File descriptor
 */
int32_t bSerialGetFd(bSerialDev_t *ptSerialDev)
{
    if (ptSerialDev == NULL) {
        B_SERIAL_ERROR("Serial port not initialized\n");
        return bSerialResult_eERROR_ARG;
    }
    return ptSerialDev->fd;
}