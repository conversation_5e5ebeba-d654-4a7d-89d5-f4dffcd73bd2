/*******************************************************************************
* ZHT Communication & technology Co.Ltd 2019-2029 Copyright reserved
* File : bJson.h
* Function : This is a light wrapper for cJSON lib interface.
    For convenient to parse cJSON object, like import and export a json file,
    and parse data type for string/double/float/int/bool/array and so on;
* Author : wuliuzhi
* Created on: 2025.07.02
*
* Modification history:
* Ver Who Date Changes
* ------------------------------------------------------------------------------
* V1.0 wuliuzhi 2025/07/02 First release
********************************************************************************/
#ifndef __B_JSON_H__
#define __B_JSON_H__

#ifdef __cplusplus
extern "C" {
#endif

#include <stdio.h>
#include <stdlib.h>
#include <stdbool.h>
#include <string.h>
#include "cJSON.h"


/**
 * @brief  Supple a JSON file and this return a cJSON object you can interrogate;
 *              It is implemented by cJSON_Parse;
 * @param  fileName: the file name;
 * @return return address is success, or null if an error occurred(in which case, errno is set appropriately);
 * @note   the caller is always responsible to free the results with cJSON_Delete;
 */
cJSON *bJsonImportFile(char *fileName);

/**
 * @brief  Convert a cJSON object and then output a JSON file;
 *              It is implemented by cJSON_Print;
 * @param  object: the cJSON object;
 * @param  fileName: the file name;
 * @return return 0 is success, or -1 if an error occurred(in which case, errno is set appropriately);
 */
int bJsonExportFile(cJSON *object, char *fileName);

/**
 * @brief  Read string value from cJSON object;
 * @param  object: the cJSON object;
 * @param  name: the item name;
 * @param  buffer: read value will stored here;
 * @param  bufferSize: the buffer size;
 * @return return 0 is success, -1 is failed;
 */
int bJsonGetString(cJSON *object, char *name, char *buffer, int bufferSize);

/**
 * @brief  Read double value from cJSON object;
 * @param  object: the cJSON object;
 * @param  name: the item name;
 * @param  value: the pointer for read value;
 * @return return 0 is success, -1 is failed;
 */
int bJsonGetDouble(cJSON *object, char *name, double *value);

/**
 * @brief  Read float value from cJSON object;
 * @param  object: the cJSON object;
 * @param  name: the item name;
 * @param  value: the pointer for read value;
 * @return return 0 is success, -1 is failed;
 */
int bJsonGetFloat(cJSON *object, char *name, float *value);

/**
 * @brief  Read int value from cJSON object;
 * @param  object: the cJSON object;
 * @param  name: the item name;
 * @param  value: the pointer for read value;
 * @return return 0 is success, -1 is failed;
 */
int bJsonGetInteger(cJSON *object, char *name, int *value);

/**
 * @brief  Read bool value from cJSON object;
 * @param  object: the cJSON object;
 * @param  name: the item name;
 * @param  value: the pointer for read value;
 * @return return 0 is success, -1 is failed;
 */
int bJsonGetBool(cJSON *object, char *name, bool *boolean);

/**
 * @brief  Read string array value from cJSON object;
 * @param  object: the cJSON object;
 * @param  name: the item name;
 * @param  strings: the pointer of string pointer array;
 * @param  count: how many size for string pointer array;
 * @param  maxLen: the max length for every string buffer;
 * @return return 0 is success, -1 is failed;
 */
int bJsonGetStringArray(cJSON *object, char *name, char **strings, int count, int maxLen);

/**
 * @brief  Read double array value from cJSON object;
 * @param  object: the cJSON object;
 * @param  name: the item name;
 * @param  strings: the pointer of string pointer array;
 * @param  count: how many size for string pointer array;
 * @param  maxLen: the max length for every string buffer;
 * @return return 0 is success, -1 is failed;
 */
int bJsonGetDoubleArray(cJSON *object, char *name, double *numbers, int count);

/**
 * @brief  Read float array value from cJSON object;
 * @param  object: the cJSON object;
 * @param  name: the item name;
 * @param  numbers: the pointer of float pointer array;
 * @param  count: how many size for float pointer array;
 * @return return 0 is success, -1 is failed;
 */
int bJsonGetFloatArray(cJSON *object, char *name, float *numbers, int count);

/**
 * @brief  Read int array value from cJSON object;
 * @param  object: the cJSON object;
 * @param  name: the item name;
 * @param  numbers: the pointer of int pointer array;
 * @param  count: how many size for int pointer array;
 * @return return 0 is success, -1 is failed;
 */
int bJsonGetIntegerArray(cJSON *object, char *name, int *numbers, int count);

/**
 * @brief  Update string value for cJSON object;
 * @param  object: the cJSON object;
 * @param  name: the item name;
 * @param  buffer: string value;
 * @return return 0 is success, -1 is failed;
 */
int bJsonUpdateString(cJSON *object, char *name, char *buffer);

/**
 * @brief  Update double value for cJSON object;
 * @param  object: the cJSON object;
 * @param  name: the item name;
 * @param  value: double value;
 * @return return 0 is success, -1 is failed;
 */
int bJsonUpdateDouble(cJSON *object, char *name, double value);

/**
 * @brief  Update float value for cJSON object;
 * @param  object: the cJSON object;
 * @param  name: the item name;
 * @param  value: float value;
 * @return return 0 is success, -1 is failed;
 */
int bJsonUpdateFloat(cJSON *object, char *name, float value);

/**
 * @brief  Update int value for cJSON object;
 * @param  object: the cJSON object;
 * @param  name: the item name;
 * @param  value: int value;
 * @return return 0 is success, -1 is failed;
 */
int bJsonUpdateInteger(cJSON *object, char *name, int value);

/**
 * @brief  Update bool value for cJSON object;
 * @param  object: the cJSON object;
 * @param  name: the item name;
 * @param  value: bool value;
 * @return return 0 is success, -1 is failed;
 */
int bJsonUpdateBool(cJSON *object, char *name, bool boolean);

/**
 * @brief  Update string array value for cJSON object;
 * @param  object: the cJSON object;
 * @param  name: the item name;
 * @param  strings: the pointer of string pointer array;
 * @param  count: how many size for string pointer array;
 * @return return 0 is success, -1 is failed;
 */
int bJsonUpdateStringArray(cJSON *object, char *name, char **strings, int count);

/**
 * @brief  Update double array value for cJSON object;
 * @param  object: the cJSON object;
 * @param  name: the item name;
 * @param  numbers: the pointer of double array;
 * @param  count: how many size for double array;
 * @return return 0 is success, -1 is failed;
 */
int bJsonUpdateDoubleArray(cJSON *object, char *name, double *numbers, int count);

/**
 * @brief  Update float array value for cJSON object;
 * @param  object: the cJSON object;
 * @param  name: the item name;
 * @param  numbers: the pointer of float array;
 * @param  count: how many size for float array;
 * @return return 0 is success, -1 is failed;
 */
int bJsonUpdateFloatArray(cJSON *object, char *name, float *numbers, int count);

/**
 * @brief  Update int array value for cJSON object;
 * @param  object: the cJSON object;
 * @param  name: the item name;
 * @param  numbers: the pointer of int array;
 * @param  count: how many size for int array;
 * @return return 0 is success, -1 is failed;
 */
int bJsonUpdateIntegerArray(cJSON *object, char *name, int *numbers, int count);

/**
 * @brief  Let string pointer array point to string array;
 * @param  strptr: the string pointer array;
 * @param  strptrNumber: the number of pointer for string pointer array;
 * @param  array: the string array;
 * @param  arrayNumber: the number of string array;
 * @param  arrayLen: the max size for every string;
 * @return return 0 is success, -1 is failed;
 */
int bJsonStrPtrToArray(char *strptr[],
    int strptrNumber, void *array, int arrayNumber, int arrayLen);

#ifdef __cplusplus
}
#endif

#endif
