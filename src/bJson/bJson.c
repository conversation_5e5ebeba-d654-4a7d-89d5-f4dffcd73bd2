/*******************************************************************************
* ZHT Communication & technology Co.Ltd 2019-2029 Copyright reserved
* File : bJson.c
* Function : This is a light wrapper for cJSON lib interface.
    For convenient to parse cJSON object, like import and export a json file,
    and parse data type for string/double/float/int/bool/array and so on;
* Author : wuliuzhi
* Created on: 2025.07.02
*
* Modification history:
* Ver Who Date Changes
* ------------------------------------------------------------------------------
* V1.0 wuliuzhi 2025/07/02 First release
********************************************************************************/
#include "bJson.h"


/**
 * @brief  Read file data and stored it to an allocate buffer;
 * @param  fileName: the file name;
 * @return return buffer address is success, or null if an error occurred(in which case, errno is set appropriately);
 */
static char *bJsonReadFile(const char *fileName)
{
    FILE *file = NULL;
    long length = 0;
    char *content = NULL;
    size_t read_chars = 0;

    /* open in read binary mode */
    file = fopen(fileName, "rb");
    if (file == NULL) {
        goto cleanup;
    }

    /* get the length */
    if (fseek(file, 0, SEEK_END) != 0) {
        goto cleanup;
    }
    length = ftell(file);
    if (length < 0) {
        goto cleanup;
    }
    if (fseek(file, 0, SEEK_SET) != 0) {
        goto cleanup;
    }

    /* allocate content buffer */
    content = (char*)cJSON_malloc((size_t)length + sizeof(""));
    if (content == NULL) {
        goto cleanup;
    }

    /* read the file into memory */
    read_chars = fread(content, sizeof(char), (size_t)length, file);
    if ((long)read_chars != length) {
        cJSON_free(content);
        content = NULL;
        goto cleanup;
    }
    content[read_chars] = '\0';

cleanup:
    if (file != NULL) {
        fclose(file);
    }

    return content;
}

/**
 * @brief  Write data to file;
 * @param  fileName: the file name;
 * @param  content: the data pointer;
 * @param  len: how many length will be writed;
 * @return return 0 is success, or -1 if an error occurred(in which case, errno is set appropriately);
 */
static int bJsonWriteFile(const char *fileName, void *content, size_t len)
{
    FILE *fp = NULL;
    size_t size = 0;

    fp = fopen(fileName, "wb+");
    if (fp == NULL) {
        return -1;
    }

    size = fwrite(content, sizeof(char), len, fp);

    fclose(fp);

    if (size == len) {
        return 0;
    } else {
        return -1;
    }
}

/**
 * @brief  Supple a JSON file and this return a cJSON object you can interrogate;
 *              It is implemented by cJSON_Parse;
 * @param  fileName: the file name;
 * @return return address is success, or null if an error occurred(in which case, errno is set appropriately);
 * @note   the caller is always responsible to free the results with cJSON_Delete;
 */
cJSON *bJsonImportFile(char *fileName)
{
    cJSON *parsed = NULL;

    char *content = bJsonReadFile(fileName);
    if (content == NULL) {
        return NULL;;
    }

    parsed = cJSON_Parse(content);

    cJSON_free(content);

    return parsed;
}

/**
 * @brief  Convert a cJSON object and then output a JSON file;
 *              It is implemented by cJSON_Print();
 * @param  object: the cJSON object;
 * @param  fileName: the file name;
 * @return return 0 is success, or -1 if an error occurred(in which case, errno is set appropriately);
 */
int bJsonExportFile(cJSON *object, char *fileName)
{
    int ret;

    char *content = cJSON_Print(object);
    if (content == NULL) {
        return -1;
    }

    ret = bJsonWriteFile(fileName, content, strlen(content));

    cJSON_free(content);

    return ret;
}

/**
 * @brief  Read string value from cJSON object;
 * @param  object: the cJSON object;
 * @param  name: the item name;
 * @param  buffer: read value will stored here;
 * @param  bufferSize: the buffer size;
 * @return return 0 is success, -1 is failed;
 */
int bJsonGetString(cJSON *object, char *name, char *buffer, int bufferSize)
{
    cJSON *item = cJSON_GetObjectItemCaseSensitive(object, name);
    if (cJSON_IsString(item) && (item->valuestring != NULL)) {
        if (buffer != NULL) {
            snprintf(buffer, bufferSize, "%s", item->valuestring);
        }
        return 0;
    } else {
        return -1;
    }
}

/**
 * @brief  Read double value from cJSON object;
 * @param  object: the cJSON object;
 * @param  name: the item name;
 * @param  value: the pointer for read value;
 * @return return 0 is success, -1 is failed;
 */
int bJsonGetDouble(cJSON *object, char *name, double *value)
{
    cJSON *item = cJSON_GetObjectItemCaseSensitive(object, name);
    if (cJSON_IsNumber(item)) {
        if (value != NULL) {
            *value = item->valuedouble;
        }
        return 0;
    } else {
        return -1;
    }
}

/**
 * @brief  Read float value from cJSON object;
 * @param  object: the cJSON object;
 * @param  name: the item name;
 * @param  value: the pointer for read value;
 * @return return 0 is success, -1 is failed;
 */
int bJsonGetFloat(cJSON *object, char *name, float *value)
{
    cJSON *item = cJSON_GetObjectItemCaseSensitive(object, name);
    if (cJSON_IsNumber(item)) {
        if (value != NULL) {
            *value = (float)item->valuedouble;
        }
        return 0;
    } else {
        return -1;
    }
}

/**
 * @brief  Read int value from cJSON object;
 * @param  object: the cJSON object;
 * @param  name: the item name;
 * @param  value: the pointer for read value;
 * @return return 0 is success, -1 is failed;
 */
int bJsonGetInteger(cJSON *object, char *name, int *value)
{
    cJSON *item = cJSON_GetObjectItemCaseSensitive(object, name);
    if (cJSON_IsNumber(item)) {
        if (value != NULL) {
            *value = (int)item->valuedouble;
        }
        return 0;
    } else {
        return -1;
    }
}

/**
 * @brief  Read bool value from cJSON object;
 * @param  object: the cJSON object;
 * @param  name: the item name;
 * @param  value: the pointer for read value;
 * @return return 0 is success, -1 is failed;
 */
int bJsonGetBool(cJSON *object, char *name, bool *boolean)
{
    cJSON *item = cJSON_GetObjectItemCaseSensitive(object, name);
    if (cJSON_IsBool(item)) {
        if (item->type == cJSON_True) {
            if (boolean != NULL) {
                *boolean = true;
            }
        } else {
            if (boolean != NULL) {
                *boolean = false;
            }
        }
        return 0;
    } else {
        return -1;
    }
}

/**
 * @brief  Read string array value from cJSON object;
 * @param  object: the cJSON object;
 * @param  name: the item name;
 * @param  strings: the pointer of string pointer array;
 * @param  count: how many size for string pointer array;
 * @param  maxLen: the max length for every string buffer;
 * @return return 0 is success, -1 is failed;
 */
int bJsonGetStringArray(cJSON *object, char *name, char **strings, int count, int maxLen)
{
    cJSON *array = NULL;
    cJSON *item = NULL;
    int arraySize = 0;

    array = cJSON_GetObjectItemCaseSensitive(object, name);
    if (cJSON_IsArray(array)) {
        arraySize = cJSON_GetArraySize(array);
        if (count < arraySize) {
            return -1;
        }

        for (int i = 0; i < arraySize; i++) {
            item = cJSON_GetArrayItem(array, i);
            if (cJSON_IsString(item)) {
                snprintf(strings[i], maxLen, "%s", item->valuestring);
            }
        }

        return 0;
    } else {
        return -1;
    }
}

/**
 * @brief  Read double array value from cJSON object;
 * @param  object: the cJSON object;
 * @param  name: the item name;
 * @param  strings: the pointer of string pointer array;
 * @param  count: how many size for string pointer array;
 * @param  maxLen: the max length for every string buffer;
 * @return return 0 is success, -1 is failed;
 */
int bJsonGetDoubleArray(cJSON *object, char *name, double *numbers, int count)
{
    cJSON *array = NULL;
    cJSON *item = NULL;
    int arraySize = 0;

    array = cJSON_GetObjectItemCaseSensitive(object, name);
    if (cJSON_IsArray(array)) {
        arraySize = cJSON_GetArraySize(array);
        if (count < arraySize) {
            return -1;
        }

        for (int i = 0; i < arraySize; i++) {
            item = cJSON_GetArrayItem(array, i);
            if (cJSON_IsNumber(item)) {
                numbers[i] = item->valuedouble;
            }
        }

        return 0;
    } else {
        return -1;
    }
}

/**
 * @brief  Read float array value from cJSON object;
 * @param  object: the cJSON object;
 * @param  name: the item name;
 * @param  numbers: the pointer of float pointer array;
 * @param  count: how many size for float pointer array;
 * @return return 0 is success, -1 is failed;
 */
int bJsonGetFloatArray(cJSON *object, char *name, float *numbers, int count)
{
    cJSON *array = NULL;
    cJSON *item = NULL;
    int arraySize = 0;

    array = cJSON_GetObjectItemCaseSensitive(object, name);
    if (cJSON_IsArray(array)) {
        arraySize = cJSON_GetArraySize(array);
        if (count < arraySize) {
            return -1;
        }

        for (int i = 0; i < arraySize; i++) {
            item = cJSON_GetArrayItem(array, i);
            if (cJSON_IsNumber(item)) {
                numbers[i] = (float)item->valuedouble;
            }
        }

        return 0;
    } else {
        return -1;
    }
}

/**
 * @brief  Read int array value from cJSON object;
 * @param  object: the cJSON object;
 * @param  name: the item name;
 * @param  numbers: the pointer of int pointer array;
 * @param  count: how many size for int pointer array;
 * @return return 0 is success, -1 is failed;
 */
int bJsonGetIntegerArray(cJSON *object, char *name, int *numbers, int count)
{
    cJSON *array = NULL;
    cJSON *item = NULL;
    int arraySize = 0;

    array = cJSON_GetObjectItemCaseSensitive(object, name);
    if (cJSON_IsArray(array)) {
        arraySize = cJSON_GetArraySize(array);
        if (count < arraySize) {
            return -1;
        }

        for (int i = 0; i < arraySize; i++) {
            item = cJSON_GetArrayItem(array, i);
            if (cJSON_IsNumber(item)) {
                numbers[i] = (int)item->valuedouble;
            }
        }
        return 0;
    } else {
        return -1;
    }
}

/**
 * @brief  Update string value for cJSON object;
 * @param  object: the cJSON object;
 * @param  name: the item name;
 * @param  buffer: string value;
 * @return return 0 is success, -1 is failed;
 */
int bJsonUpdateString(cJSON *object, char *name, char *buffer)
{
    cJSON *item = cJSON_GetObjectItemCaseSensitive(object, name);
    if (cJSON_IsString(item) && (item->valuestring != NULL)) {
        cJSON_SetValuestring(item, buffer);
        return 0;
    } else {
        return -1;
    }
}

/**
 * @brief  Update double value for cJSON object;
 * @param  object: the cJSON object;
 * @param  name: the item name;
 * @param  value: double value;
 * @return return 0 is success, -1 is failed;
 */
int bJsonUpdateDouble(cJSON *object, char *name, double value)
{
    cJSON *item = cJSON_GetObjectItemCaseSensitive(object, name);
    if (cJSON_IsNumber(item)) {
        cJSON_SetNumberValue(item, value);
        return 0;
    } else {
        return -1;
    }
}

/**
 * @brief  Update float value for cJSON object;
 * @param  object: the cJSON object;
 * @param  name: the item name;
 * @param  value: float value;
 * @return return 0 is success, -1 is failed;
 */
int bJsonUpdateFloat(cJSON *object, char *name, float value)
{
    cJSON *item = cJSON_GetObjectItemCaseSensitive(object, name);
    if (cJSON_IsNumber(item)) {
        cJSON_SetNumberValue(item, (double)value);
        return 0;
    } else {
        return -1;
    }
}

/**
 * @brief  Update int value for cJSON object;
 * @param  object: the cJSON object;
 * @param  name: the item name;
 * @param  value: int value;
 * @return return 0 is success, -1 is failed;
 */
int bJsonUpdateInteger(cJSON *object, char *name, int value)
{
    cJSON *item = cJSON_GetObjectItemCaseSensitive(object, name);
    if (cJSON_IsNumber(item)) {
        cJSON_SetNumberValue(item, (double)value);
        return 0;
    } else {
        return -1;
    }
}

/**
 * @brief  Update bool value for cJSON object;
 * @param  object: the cJSON object;
 * @param  name: the item name;
 * @param  value: bool value;
 * @return return 0 is success, -1 is failed;
 */
int bJsonUpdateBool(cJSON *object, char *name, bool boolean)
{
    cJSON *item = cJSON_GetObjectItemCaseSensitive(object, name);
    if (cJSON_IsBool(item)) {
        item->type = (cJSON_bool)boolean;
        return 0;
    } else {
        return -1;
    }
}

/**
 * @brief  Update string array value for cJSON object;
 * @param  object: the cJSON object;
 * @param  name: the item name;
 * @param  strings: the pointer of string pointer array;
 * @param  count: how many size for string pointer array;
 * @return return 0 is success, -1 is failed;
 */
int bJsonUpdateStringArray(cJSON *object, char *name, char **strings, int count)
{
    cJSON *array = NULL;
    int arraySize = 0;
    cJSON *item = NULL;

    array = cJSON_GetObjectItemCaseSensitive(object, name);
    if (cJSON_IsArray(array)) {
        arraySize = cJSON_GetArraySize(array);
        if (count < arraySize) {
            return -1;
        }

        for (int i = 0; i < arraySize; i++) {
            item = cJSON_GetArrayItem(array, i);
            if (cJSON_IsString(item) && (item->valuestring != NULL)) {
                cJSON_SetValuestring(item, strings[i]);
            }
        }
        return 0;
    } else {
        return -1;
    }
}

/**
 * @brief  Update double array value for cJSON object;
 * @param  object: the cJSON object;
 * @param  name: the item name;
 * @param  numbers: the pointer of double array;
 * @param  count: how many size for double array;
 * @return return 0 is success, -1 is failed;
 */
int bJsonUpdateDoubleArray(cJSON *object, char *name, double *numbers, int count)
{
    cJSON *array = NULL;
    int arraySize = 0;
    cJSON *item = NULL;

    array = cJSON_GetObjectItemCaseSensitive(object, name);
    if (cJSON_IsArray(array)) {
        arraySize = cJSON_GetArraySize(array);
        if (count < arraySize) {
            return -1;
        }

        for (int i = 0; i < arraySize; i++) {
            item = cJSON_GetArrayItem(array, i);
            if (cJSON_IsNumber(item)) {
                cJSON_SetNumberValue(item, numbers[i]);
            }
        }
        return 0;
    } else {
        return -1;
    }
}

/**
 * @brief  Update float array value for cJSON object;
 * @param  object: the cJSON object;
 * @param  name: the item name;
 * @param  numbers: the pointer of float array;
 * @param  count: how many size for float array;
 * @return return 0 is success, -1 is failed;
 */
int bJsonUpdateFloatArray(cJSON *object, char *name, float *numbers, int count)
{
    cJSON *array = NULL;
    int arraySize = 0;
    cJSON *item = NULL;

    array = cJSON_GetObjectItemCaseSensitive(object, name);
    if (cJSON_IsArray(array)) {
        arraySize = cJSON_GetArraySize(array);
        if (count < arraySize) {
            return -1;
        }

        for (int i = 0; i < arraySize; i++) {
            item = cJSON_GetArrayItem(array, i);
            if (cJSON_IsNumber(item)) {
                cJSON_SetNumberValue(item, (double)numbers[i]);
            }
        }
        return 0;
    } else {
        return -1;
    }
}

/**
 * @brief  Update int array value for cJSON object;
 * @param  object: the cJSON object;
 * @param  name: the item name;
 * @param  numbers: the pointer of int array;
 * @param  count: how many size for int array;
 * @return return 0 is success, -1 is failed;
 */
int bJsonUpdateIntegerArray(cJSON *object, char *name, int *numbers, int count)
{
    cJSON *array = NULL;
    int arraySize = 0;
    cJSON *item = NULL;

    array = cJSON_GetObjectItemCaseSensitive(object, name);
    if (cJSON_IsArray(array)) {
        arraySize = cJSON_GetArraySize(array);
        if (count < arraySize) {
            return -1;
        }

        for (int i = 0; i < arraySize; i++) {
            item = cJSON_GetArrayItem(array, i);
            if (cJSON_IsNumber(item)) {
                cJSON_SetNumberValue(item, (double)numbers[i]);
            }
        }
        return 0;
    } else {
        return -1;
    }
}

/**
 * @brief  Let string pointer array point to string array;
 * @param  strptr: the string pointer array;
 * @param  strptrNumber: the number of pointer for string pointer array;
 * @param  array: the string array;
 * @param  arrayNumber: the number of string array;
 * @param  arrayLen: the max size for every string;
 * @return return 0 is success, -1 is failed;
 */
int bJsonStrPtrToArray(char *strptr[],
    int strptrNumber, void *array, int arrayNumber, int arrayLen)
{
    char *dst = array;

    if (strptr == NULL || array == NULL || (strptrNumber != arrayNumber)) {
        return -1;
    }

    for (int i = 0; i < arrayNumber; i++) {
        strptr[i] = &dst[i * arrayLen];
    }

    return 0;
}
