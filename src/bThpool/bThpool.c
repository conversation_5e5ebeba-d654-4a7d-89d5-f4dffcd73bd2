/*******************************************************************************
* ZHT Communication & technology Co.Ltd 2019-2029 Copyright reserved
* File : bThpool.c
* Function : Base on linux, Thread Pool
* Author : yangzuogui
* Created on: 2025.08.05
*
* Modification history:
* Ver Who Date Changes
* ------------------------------------------------------------------------------
* V1.0 yangzuogui 2025/08/05 First release
********************************************************************************/

#define _POSIX_C_SOURCE 200809L
#include <unistd.h>
#include <signal.h>
#include <stdio.h>
#include <stdlib.h>
#include <pthread.h>
#include <errno.h>
#include <time.h>
#include <string.h>
#if defined(__linux__)
#include <sys/prctl.h>
#endif
#include "bThpool.h"

// 使用标准库函数替代BSL函数
#define malloc malloc
#define free free
#define BSL_DBG printf


// 移除全局变量，改为线程池实例变量


typedef struct bsem {
    pthread_mutex_t mutex;
    pthread_cond_t cond;
    int v;
} bsem_t;


/* Job */
typedef struct job {
    struct job*  prev;                                  /* pointer to previous job   */
    void (*function)(void *parent, void* arg);        /* function pointer          */
    void *arg;                                         /* function's argument       */
}job_t;


/* Job queue */
typedef struct jobqueue {
    pthread_mutex_t rwmutex;             /* used for queue r/w access */
    job_t *front;                         /* pointer to front of queue */
    job_t *rear;                          /* pointer to rear  of queue */
    bsem_t *hasJobs;                      /* flag as binary semaphore  */
    int len;                           /* number of jobs in queue   */
}jobqueue_t;


/* Thread */
typedef struct thread {
    int       id;                        /* friendly id               */
    pthread_t pthread;                   /* pointer to actual thread  */
    void *ptThpool;            /* access to thpool          */
    char name[32];
}thread_t;


/* Threadpool */
struct bThpool {
    thread_t **threads;                     /* pointer to threads        */
    int numThreadsAlive;           /* threads currently alive   */
    int numThreadsWorking;         /* threads currently working */
    int threadsKeepalive;          /* threads keepalive flag    */
    int threadsOnHold;             /* threads on hold flag      */
    int numThreadsTotal;                    /* total number of threads   */
    pthread_mutex_t thcountLock;           /* used for thread count etc */
    pthread_cond_t threadsAllIdle;         /* signal to bThpoolWait     */
    pthread_cond_t threadsInitialized;     /* signal when all threads initialized */
    thread_t jobqueue;                     /* job queue                 */
    void *parent;
    char *name;
};



/**
 * @brief  Initialize binary semaphore
 * @param  ptBsem: Binary semaphore pointer;
 * @param  value: Initial value (0 or 1);
 * @return success if return 0
 */
static int bsemInit(bsem_t *ptBsem, int value)
{
    if (value < 0 || value > 1) {
        BSL_DBG("bsemInit(): Binary semaphore can take only values 1 or 0\n");
        return -1;
    }
    pthread_mutex_init(&(ptBsem->mutex), NULL);
    pthread_cond_init(&(ptBsem->cond), NULL);
    ptBsem->v = value;
    return 0;
}


/* Reset semaphore to 0 */
static void bsemReset(bsem_t *ptBsem)
{
    pthread_mutex_lock(&ptBsem->mutex);
    ptBsem->v = 0;
    pthread_mutex_unlock(&ptBsem->mutex);
}


/* Post to at least one thread */
static void bsemPost(bsem_t *ptBsem) 
{
    pthread_mutex_lock(&ptBsem->mutex);
    ptBsem->v = 1;
    pthread_cond_signal(&ptBsem->cond);
    pthread_mutex_unlock(&ptBsem->mutex);
}


/* Post to all threads */
static void bsemPostAll(bsem_t *ptBsem) 
{
    pthread_mutex_lock(&ptBsem->mutex);
    ptBsem->v = 1;
    pthread_cond_broadcast(&ptBsem->cond);
    pthread_mutex_unlock(&ptBsem->mutex);
}


/* Wait on semaphore until semaphore has value 0 */
static void bsemWait(bsem_t *ptBsem) 
{
    pthread_mutex_lock(&ptBsem->mutex);
    while (ptBsem->v != 1) {
        pthread_cond_wait(&ptBsem->cond, &ptBsem->mutex);
    }
    ptBsem->v = 0;
    pthread_mutex_unlock(&ptBsem->mutex);
}

/**
 * @brief  Initialize job queue
 * @param  ptJobqueue: Job queue pointer;
 * @return return 0 if successful, or -1 if an error occurred
 */
static int jobQueueInit(jobqueue_t *ptJobqueue)
{
    ptJobqueue->len = 0;
    ptJobqueue->front = NULL;
    ptJobqueue->rear  = NULL;

    ptJobqueue->hasJobs = (bsem_t *)malloc(sizeof(bsem_t ));
    if (ptJobqueue->hasJobs == NULL){
        return -1;
    }

    pthread_mutex_init(&(ptJobqueue->rwmutex), NULL);
    return bsemInit(ptJobqueue->hasJobs, 0);
}


/**
 * @brief  Clear all jobs from the queue
 * @param  ptJobqueue: Job queue pointer;
 * @return None
 */
static void jobQueueClear(jobqueue_t *ptJobqueue)
{
    while(ptJobqueue->len){
        free(jobQueuePop(ptJobqueue));
    }
    ptJobqueue->front = NULL;
    ptJobqueue->rear  = NULL;
    bsemReset(ptJobqueue->hasJobs);
    ptJobqueue->len = 0;
}


/**
 * @brief  Add job to the end of queue
 * @param  ptJobqueue: Job queue pointer;
 * @param  newjob: Job to be added;
 * @return None
 */
static int jobQueuePush(jobqueue_t *ptJobqueue, job_t *newjob)
{
    if(ptJobqueue == NULL || newjob == NULL){
        return -1;
    }
    pthread_mutex_lock(&ptJobqueue->rwmutex);
    newjob->prev = NULL;

    switch(ptJobqueue->len){

        case 0:  /* if no jobs in queue */
                    ptJobqueue->front = newjob;
                    ptJobqueue->rear  = newjob;
                    break;

        default: /* if jobs in queue */
                    ptJobqueue->rear->prev = newjob;
                    ptJobqueue->rear = newjob;

    }
    ptJobqueue->len++;

    bsemPost(ptJobqueue->hasJobs);
    pthread_mutex_unlock(&ptJobqueue->rwmutex);
    return 0;
}


/**
 * @brief  Get and remove first job from queue
 * @param  ptJobqueue: Job queue pointer;
 * @return return job pointer if queue is not empty, otherwise return NULL
 */
static job_t *jobQueuePop(jobqueue_t *ptJobqueue)
{
    if(ptJobqueue == NULL){
        return NULL;
    }
    pthread_mutex_lock(&ptJobqueue->rwmutex);
    job_t *pJob = ptJobqueue->front;
    switch(ptJobqueue->len){
        case 0:  /* if no jobs in queue */
            break;

        case 1:  /* if one job in queue */
            ptJobqueue->front = NULL;
            ptJobqueue->rear  = NULL;
            ptJobqueue->len = 0;
            break;

        default: /* if >1 jobs in queue */
            ptJobqueue->front = pJob->prev;
            ptJobqueue->len--;
            /* more than one job in queue -> post it */
            bsemPost(ptJobqueue->hasJobs);
    }

    pthread_mutex_unlock(&ptJobqueue->rwmutex);
    return pJob;
}


/**
 * @brief  Destroy job queue and free all resources
 * @param  ptJobqueue: Job queue pointer;
 * @return None
 */
static void jobQueueDestroy(jobqueue_t *ptJobqueue)
{
    if(ptJobqueue == NULL){
        return;
    }
    jobQueueClear(ptJobqueue);

    /* Destroy semaphore resources */
    if (ptJobqueue->hasJobs) {
        pthread_mutex_destroy(&ptJobqueue->hasJobs->mutex);
        pthread_cond_destroy(&ptJobqueue->hasJobs->cond);
        free(ptJobqueue->hasJobs);
    }

    /* Destroy queue mutex */
    pthread_mutex_destroy(&ptJobqueue->rwmutex);
}


/**
 * @brief  Initialize a worker thread
 * @param  ptThpool: Thread pool handler;
 * @param  ptThread: Pointer to thread structure pointer;
 * @param  id: Thread ID;
 * @return return 0 if successful, or -1 if an error occurred
 */
static int threadInit(bThpool_t *ptThpool, thread_t **ptThread, int id)
{
    *ptThread = (thread_t *)malloc(sizeof(thread_t ));
    if (*ptThread == NULL){
        BSL_DBG("threadInit(): Could not allocate memory for thread\n");
        return -1;
    }

    (*ptThread)->ptThpool = ptThpool;
    (*ptThread)->id       = id;

    /* Create thread and check for errors */
    int ret = pthread_create(&(*ptThread)->pthread, NULL, (void * (*)(void *)) threadDo, (*ptThread));
    if (ret != 0) {
        BSL_DBG("threadInit(): Could not create thread %d: %s\n", id, strerror(ret));
        free(*ptThread);
        *ptThread = NULL;
        return -1;
    }

    pthread_detach((*ptThread)->pthread);
    return 0;
}


/**
 * @brief  Worker thread main function
 * @param  ptThread: Thread structure pointer;
 * @return return NULL when thread exits
 */
static void* threadDo(thread_t *ptThread)
{
    /* Assure all threads have been created before starting serving */
    bThpool_t *ptThpool = ptThread->ptThpool;

    /* Set thread name for profiling and debuging */
    char thread_name[32] = {0};
    snprintf(thread_name, 32, "tp-%s-%d", ptThpool->name, ptThread->id);
    prctl(PR_SET_NAME, thread_name);

    /* Mark thread as alive (initialized) */
    pthread_mutex_lock(&ptThpool->thcountLock);
    ptThpool->numThreadsAlive += 1;
    if (ptThpool->numThreadsAlive == ptThpool->numThreadsTotal) {
        pthread_cond_signal(&ptThpool->threadsInitialized);
    }
    pthread_mutex_unlock(&ptThpool->thcountLock);

    while(ptThpool->threadsKeepalive){
        bsemWait(ptThpool->jobqueue.hasJobs);
        if (ptThpool->threadsKeepalive){
            pthread_mutex_lock(&ptThpool->thcountLock);
            ptThpool->numThreadsWorking++;
            pthread_mutex_unlock(&ptThpool->thcountLock);

            /* Read job from queue and execute it */
            void (*pFunc)(void*, void*);
            void *pArgs;
            job *pJob = jobQueuePop(&ptThpool->jobqueue);
            if (pJob) {
                pFunc = pJob->function;
                pArgs  = pJob->arg;
                pFunc(ptThpool->parent, pArgs);
                free(pJob);
            }

            pthread_mutex_lock(&ptThpool->thcountLock);
            ptThpool->numThreadsWorking--;
            if (!ptThpool->numThreadsWorking) {
                pthread_cond_signal(&ptThpool->threadsAllIdle);
            }
            pthread_mutex_unlock(&ptThpool->thcountLock);
        }
    }
    pthread_mutex_lock(&ptThpool->thcountLock);
    ptThpool->numThreadsAlive--;
    pthread_mutex_unlock(&ptThpool->thcountLock);

    return NULL;
}


/* Frees a thread  */
static void threadDestroy(thread* ptThread)
{
    free(ptThread);
}



/**
 * @brief  Create a thread pool
 * @param  parent: Parent context pointer, will be passed to worker functions;
 * @param  numThreads: Number of worker threads to create;
 * @param  name: Name of the thread pool for debugging;
 * @return return a not-null pointer as bThpool_t if successful, else will return NULL if failed
 */
bThpool_t *bThpoolCreate(void *parent, int numThreads, char *name)
{
    if (numThreads <= 0){
        BSL_DBG("bThpoolCreate(): Invalid number of threads (%d)\n", numThreads);
        return NULL;
    }

    /* Make new thread pool */
    bThpool_t *ptThpool;
    ptThpool = (bThpool_t *)malloc(sizeof(struct bThpool));
    if (ptThpool == NULL){
        BSL_DBG("bThpoolCreate(): Could not allocate memory for thread pool\n");
        return NULL;
    }
    ptThpool->numThreadsAlive   = 0;
    ptThpool->numThreadsWorking = 0;
    ptThpool->threadsKeepalive  = 1;
    ptThpool->threadsOnHold     = 0;
    ptThpool->numThreadsTotal   = numThreads;
    ptThpool->parent = parent;
    ptThpool->name = name;

    /* Initialise the job queue */
    if (jobQueueInit(&ptThpool->jobqueue) == -1){
        BSL_DBG("bThpoolCreate(): Could not allocate memory for job queue\n");
        free(ptThpool);
        return NULL;
    }

    /* Make threads in pool */
    ptThpool->threads = (struct thread**)malloc(numThreads * sizeof(struct thread *));
    if (ptThpool->threads == NULL){
        BSL_DBG("bThpoolCreate(): Could not allocate memory for threads\n");
        jobQueueDestroy(&ptThpool->jobqueue);
        free(ptThpool);
        return NULL;
    }

    pthread_mutex_init(&(ptThpool->thcountLock), NULL);
    pthread_cond_init(&ptThpool->threadsAllIdle, NULL);
    pthread_cond_init(&ptThpool->threadsInitialized, NULL);

    /* Thread init */
    int n;
    for (n = 0; n < numThreads; n++){
        if (threadInit(ptThpool, &ptThpool->threads[n], n) != 0) {
            BSL_DBG("bThpoolCreate(): Failed to create thread %d\n", n);
            /* Clean up already created threads */
            ptThpool->threadsKeepalive = 0;
            for (int i = 0; i < n; i++) {
                if (ptThpool->threads[i] != NULL) {
                    threadDestroy(ptThpool->threads[i]);
                }
            }
            jobQueueDestroy(&ptThpool->jobqueue);
            pthread_mutex_destroy(&ptThpool->thcountLock);
            pthread_cond_destroy(&ptThpool->threadsAllIdle);
            pthread_cond_destroy(&ptThpool->threadsInitialized);
            free(ptThpool->threads);
            free(ptThpool);
            return NULL;
        }
        BSL_DBG("THPOOL_DEBUG: Created thread %d in pool \n", n);
    }

    /* Wait for threads to initialize */
    pthread_mutex_lock(&ptThpool->thcountLock);
    while (ptThpool->numThreadsAlive != numThreads) {
        pthread_cond_wait(&ptThpool->threadsInitialized, &ptThpool->thcountLock);
    }
    pthread_mutex_unlock(&ptThpool->thcountLock);

    return ptThpool;
}


/**
 * @brief  Add work to the thread pool
 * @param  ptThpool: Thread pool handler;
 * @param  function_p: Function pointer to be executed by worker thread;
 * @param  arg_p: Argument to be passed to the function;
 * @return return 0 if successful, or negative number if an error occurred
 */
int bThpoolAddWork(bThpool_t *ptThpool, void (*function_p)(void*, void*), void* arg_p)
{
    job* newjob;

    newjob=(struct job*)malloc(sizeof(struct job));
    if (newjob == NULL){
        BSL_DBG("bThpoolAddWork(): Could not allocate memory for new job\n");
        return -1;
    }

    /* add function and argument */
    newjob->function = function_p;
    newjob->arg = arg_p;

    /* add job to queue */
    jobQueuePush(&ptThpool->jobqueue, newjob);

    return 0;
}


/**
 * @brief  Wait until all jobs have finished
 * @param  ptThpool: Thread pool handler;
 * @return None
 */
void bThpoolWait(bThpool_t *ptThpool)
{
    pthread_mutex_lock(&ptThpool->thcountLock);
    while (ptThpool->jobqueue.len || ptThpool->numThreadsWorking) {
        pthread_cond_wait(&ptThpool->threadsAllIdle, &ptThpool->thcountLock);
    }
    pthread_mutex_unlock(&ptThpool->thcountLock);
}


/**
 * @brief  Destroy the thread pool and release all resources
 * @param  ptThpool: Thread pool handler;
 * @return None
 */
void bThpoolRelease(bThpool_t *ptThpool)
{
    /* No need to destory if it's NULL */
    if (ptThpool == NULL){
        return ;
    }

    volatile int threadsTotal = ptThpool->numThreadsAlive;

    /* End each thread 's infinite loop */
    ptThpool->threadsKeepalive = 0;

    /* Give one second to kill idle threads */
    double TIMEOUT = 1.0;
    time_t start, end;
    double tpassed = 0.0;
    time (&start);
    while (tpassed < TIMEOUT && ptThpool->numThreadsAlive){
        bsemPostAll(ptThpool->jobqueue.hasJobs);
        time (&end);
        tpassed = difftime(end, start);
    }

    /* Poll remaining threads */
    while (ptThpool->numThreadsAlive){
        bsemPostAll(ptThpool->jobqueue.hasJobs);
        sleep(1);
    }

    /* Job queue cleanup */
    jobQueueDestroy(&ptThpool->jobqueue);

    /* Destroy pthread resources */
    pthread_mutex_destroy(&ptThpool->thcountLock);
    pthread_cond_destroy(&ptThpool->threadsAllIdle);
    pthread_cond_destroy(&ptThpool->threadsInitialized);

    /* Deallocs */
    int n;
    for (n = 0; n < threadsTotal; n++){
        threadDestroy(ptThpool->threads[n]);
    }
    free(ptThpool->threads);
    free(ptThpool);
}


/**
 * @brief  Pause all threads in thread pool
 * @param  ptThpool: Thread pool handler;
 * @return None
 */
void bThpoolPause(bThpool_t *ptThpool)
{
    if (ptThpool == NULL) {
        return;
    }

    ptThpool->threadsOnHold = 1;

    pthread_mutex_lock(&ptThpool->thcountLock);
    int threadsAlive = ptThpool->numThreadsAlive;
    pthread_mutex_unlock(&ptThpool->thcountLock);

    int n;
    for (n = 0; n < threadsAlive && n < ptThpool->numThreadsTotal; n++){
        if (ptThpool->threads[n] != NULL) {
            pthread_kill(ptThpool->threads[n]->pthread, SIGUSR1);
        }
    }
}


/**
 * @brief  Resume all threads in thread pool
 * @param  ptThpool: Thread pool handler;
 * @return None
 */
void bThpoolResume(bThpool_t *ptThpool)
{
    if (ptThpool == NULL) {
        return;
    }
    ptThpool->threadsOnHold = 0;
}


/**
 * @brief  Get number of threads currently working
 * @param  ptThpool: Thread pool handler;
 * @return return number of working threads
 */
int bThpoolNumThreadsWorking(bThpool_t *ptThpool)
{
    return ptThpool->numThreadsWorking;
}



