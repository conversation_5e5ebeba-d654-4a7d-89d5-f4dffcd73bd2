/*******************************************************************************
* ZHT Communication & technology Co.Ltd 2019-2029 Copyright reserved
* File : bThpool.h
* Function : Base on linux, Thread Pool
* Author : yangzuogui
* Created on: 2025.08.05
*
* Modification history:
* Ver Who Date Changes
* ------------------------------------------------------------------------------
* V1.0 yangzuogui 2025/08/05 First release
********************************************************************************/
#ifndef __BTHPOOL_H__
#define __BTHPOOL_H__

#ifdef __cplusplus
extern "C" {
#endif

typedef struct bThpool bThpool_t;

/**
 * @brief  Create a thread pool
 * @param  parent: Parent context pointer, will be passed to worker functions;
 * @param  numThreads: Number of worker threads to create;
 * @param  name: Name of the thread pool for debugging;
 * @return return a not-null pointer as bThpool_t if successful, else will return NULL if failed
 */
bThpool_t *bThpoolCreate(void *parent, int numThreads, char *name);

/**
 * @brief  Add work to the thread pool
 * @param  ptThpool: Thread pool handler;
 * @param  function_p: Function pointer to be executed by worker thread;
 * @param  arg_p: Argument to be passed to the function;
 * @return return 0 if successful, or negative number if an error occurred
 */
int bThpoolAddWork(bThpool_t *ptThpool, void (*function_p)(void*, void*), void* arg_p);

/**
 * @brief  Wait until all jobs have finished
 * @param  ptThpool: Thread pool handler;
 * @return None
 */
void bThpoolWait(bThpool_t *ptThpool);

/**
 * @brief  Pause all threads in thread pool
 * @param  ptThpool: Thread pool handler;
 * @return None
 */
void bThpoolPause(bThpool_t *ptThpool);

/**
 * @brief  Resume all threads in thread pool
 * @param  ptThpool: Thread pool handler;
 * @return None
 */
void bThpoolResume(bThpool_t *ptThpool);

/**
 * @brief  Destroy the thread pool and release all resources
 * @param  ptThpool: Thread pool handler;
 * @return None
 */
void bThpoolRelease(bThpool_t *ptThpool);

/**
 * @brief  Get number of threads currently working
 * @param  ptThpool: Thread pool handler;
 * @return return number of working threads
 */
int bThpoolNumThreadsWorking(bThpool_t *ptThpool);


#ifdef __cplusplus
}
#endif

#endif
