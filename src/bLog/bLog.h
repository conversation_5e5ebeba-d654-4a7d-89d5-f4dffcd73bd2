/*******************************************************************************
* ZHT Communication & technology Co.Ltd 2019-2029 Copyright reserved
* File : bLog.h
* Function : Logging library inferface refference from zlog;
* Author : wuliuzhi
* Created on: 2025.07.30
*
* Modification history:
* Ver Who Date Changes
* ------------------------------------------------------------------------------
* V1.0 wuliuzhi 2025/07/30 First release
********************************************************************************/
#ifndef __B_LOG_H__
#define __B_LOG_H__

#ifdef __cplusplus
extern "C" {
#endif

#include <stdarg.h> /* for va_list */


#define EMPTY()
#define BLOG_ASSERT(expr, rv) \
    if(!(expr)) { \
        BLOG_ERROR(#expr" is null or 0"); \
        return rv; \
    } 

enum bLogProfileLevel {
    BLOG_LEVEL_DEBUG = 0,
    BLOG_LEVEL_WARN = 1,
    BLOG_LEVEL_ERROR = 2
};

/**
 * @brief  Introduce for BLOG_ interface:
 *  If you want output logger to a specify file, please config 
 *  the environment varible 'BLOG_PROFILE_DEBUG' and 'BLOG_PROFILE_ERROR'
 */
#define BLOG_DEBUG(...) \
    bLogProfileInner(BLOG_LEVEL_DEBUG, __FILE__, __LINE__, __VA_ARGS__)

#define BLOG_WARN(...) \
    bLogProfileInner(BLOG_LEVEL_WARN, __FILE__, __LINE__, __VA_ARGS__)

#define BLOG_ERROR(...) \
    bLogProfileInner(BLOG_LEVEL_ERROR, __FILE__, __LINE__, __VA_ARGS__)

#define BLOG_PROFILE(level, ...) \
    bLogProfileInner(level, __FILE__, __LINE__, __VA_ARGS__)


int bLogProfileInner(int level, 
    const char *file, const long line,
    const char *fmt, ...);

#ifdef __cplusplus
}
#endif

#endif
