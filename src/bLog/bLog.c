/*******************************************************************************
* ZHT Communication & technology Co.Ltd 2019-2029 Copyright reserved
* File : bLog.c
* Function : Logging library inferface refference from zlog;
* Author : wuliuzhi
* Created on: 2025.07.30
*
* Modification history:
* Ver Who Date Changes
* ------------------------------------------------------------------------------
* V1.0 wuliuzhi 2025/07/30 First release
********************************************************************************/

#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include <errno.h>
#include <stdarg.h>
#include <time.h>
#include <sys/types.h>
#include <unistd.h>

#include "bLog.h"


static void bLogTime(char *time_str, size_t time_str_size)
{
    time_t tt;

	time(&tt);
#ifdef _WIN32
    struct tm *local_time;
    local_time = localtime(&tt);
    strftime(time_str, time_str_size, "%m-%d %H:%M:%S", local_time);
#else
    struct tm local_time;
    localtime_r(&tt, &local_time);
    strftime(time_str, time_str_size, "%m-%d %H:%M:%S", &local_time);
#endif

    return;
}

int bLogProfileInner(int level, const char *file, const long line, const char *fmt, ...)
{
    va_list args;
    char time_str[20 + 1];
    FILE *fp = NULL;

    static char *debug_log = NULL;
    static char *error_log = NULL;
    static size_t init_flag = 0;

    if (!init_flag) {
        init_flag = 1;
        debug_log = getenv("BLOG_PROFILE_DEBUG");
        error_log = getenv("BLOG_PROFILE_ERROR");
    }

    switch (level) {
    case BLOG_LEVEL_DEBUG:
        if (debug_log == NULL) return 0;
        fp = fopen(debug_log, "a");
        if (!fp) return -1;
        bLogTime(time_str, sizeof(time_str));
        fprintf(fp, "%s DEBUG (%d:%s:%ld) ", time_str, getpid(), file, line);
        break;
    case BLOG_LEVEL_WARN:
        if (error_log == NULL) return 0;
        fp = fopen(error_log, "a");
        if (!fp) return -1;
        bLogTime(time_str, sizeof(time_str));
        fprintf(fp, "%s WARN  (%d:%s:%ld) ", time_str, getpid(), file, line);
        break;
    case BLOG_LEVEL_ERROR:
        if (error_log == NULL) return 0;
        fp = fopen(error_log, "a");
        if (!fp) return -1;
        bLogTime(time_str, sizeof(time_str));
        fprintf(fp, "%s ERROR (%d:%s:%ld) ", time_str, getpid(), file, line);
        break;
    default:
        return -1;
    }

    /* writing file twice(time & msg) is not atomic
        * may cause cross
        * but avoid log size limit */
    va_start(args, fmt);
    vfprintf(fp, fmt, args);
    va_end(args);
    fprintf(fp, "\n");

    fclose(fp);
    return 0;
}
