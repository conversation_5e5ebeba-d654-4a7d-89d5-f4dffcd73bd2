/*******************************************************************************
* ZHT Communication & technology Co.Ltd 2019-2029 Copyright reserved
* File : bJsonPath.h
* Function : This is a light wrapper for cJSON lib interface.
    Provides path support for operations on cJSON objects
* Author : wanghy
* Created on: 2025.07.14
*
* Modification history:
* Ver Who Date Changes
* ------------------------------------------------------------------------------
* V1.0 wagnhy 2025/07/14 First release
********************************************************************************/
#ifndef __B_JSON_PATH_H__
#define __B_JSON_PATH_H__

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <stdbool.h>
#include "cJSON.h"

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

/*********************************************************************************
 * JSON path format: {keName}{{.number|.keyName}...}
 *      number: Number indicates the array index
 *      keyName: he name of the json node
 * example
 * {
        "store": {
            "books": [
                {"title": "Book1", "authors": ["Author1", "Author2"]},
                {"title": "Book2", "authors": ["Author3"]}
            ],
            "location": "Downtown"
        }
    }
    path:                                   object:
        store.books.0.authors.1                 Author2
        store.location                          Downtown
******************************************************************************/

#define BJSON_PATH_MAX 1024  /* the maximum length of the path */

/*****************************************************************************************************
 * Get the object
*****************************************************************************************************/
/**
 * @brief  Get the item;
 * @param  object: the cJSON object;
 * @param  path: the item path;
 * @return Return null if item does not exist, otherwise point to item;
 */
cJSON *bJsonPathGet(const cJSON *object, const char *path);

/**
 * @brief  Get the size of an array item;
 * @param  object: the cJSON object;
 * @param  path: the item path;
 * @return Returns the number of elements in the array;
 */
int bJsonPathGetArraySize(const cJSON *object, const char *path);

/**
 * @brief  Get the double object value
 * @param  object: the cJSON object;
 * @param  path: the item path;
 * @param  number: Points to the double;
 * @return return 0 for sucess, -1 is failed;
 */
int bJsonPathGetDouble(const cJSON *object, const char *path, double *number);

/**
 * @brief  Get the float object value
 * @param  object: the cJSON object;
 * @param  path: the item path;
 * @param  number: Points to the float;
 * @return return 0 for sucess, -1 is failed;
 */
int bJsonPathGetFloat(const cJSON *object, const char *path, float *number);

/**
 * @brief  Get the integer object value
 * @param  object: the cJSON object;
 * @param  path: the item path;
 * @param  number: Points to the integer;
 * @return return 0 for sucess, -1 is failed;
 */
int bJsonPathGetInteger(const cJSON *object, const char *path, int *number);

/**
 * @brief  Get the string object value
 * @param  object: the cJSON object;
 * @param  path: the item path;
 * @param  buffer: read value will stored here;
 * @param  bufferSize: the "buffer" size;
 * @return return 0 for sucess, -1 is failed;
 */
int bJsonPathGetString(const cJSON *object, const char *path, char *buffer, int bufferSize);

/**
 * @brief  Get the double array object value
 * @param  object: the cJSON object;
 * @param  path: the item path;
 * @param  numbers: Points to the double array;
 * @param  counts: how many size for double array;
 * @return The number of double array elements obtained;
 */
int bJsonPathGetDoubleArray(const cJSON *object, const char *path, double *numbers, int count);

/**
 * @brief  Get the float array object value
 * @param  object: the cJSON object;
 * @param  path: the item path;
 * @param  numbers: Points to the float array;
 * @param  counts: how many size for float array;
 * @return The number of float array elements obtained;
 */
int bJsonPathGetFloatArray(const cJSON *object, const char *path, float *numbers, int count);

/**
 * @brief  Get the integer array object value
 * @param  object: the cJSON object;
 * @param  path: the item path;
 * @param  numbers: Points to the interger array;
 * @param  counts: how many size for interger array;
 * @return The number of interger array elements obtained;
 */
int bJsonPathGetIntegerArray(const cJSON *object, const char *path, int *numbers, int count);

/**
 * @brief  Get the string array object value
 * @param  object: the cJSON object;
 * @param  path: the item path;
 * @param  strings: Points to the string array(A two-dimensional array of char types represents a string array)
 * @param  rows: Maximum number of strings (number of rows in a two-dimensional data)
 * @param  clows: Maximum length of the string (number of columns in two-dimensional data)
 * @return The number of string array elements obtained
 */
int bJsonPath2dArrayGetStringArray(const cJSON *object, const char *path, char *strings, int rows, int clos);

/**
 * @brief  Get the string array object value
 * @param  object: the cJSON object;
 * @param  path: the item path;
 * @param  strings: Points to the string array("Strings" are arrays of pointers)
 * @param  stringNums: Number of strings
 * @param  stringSize: The maximum length of the "strings[n]"
 * @return The number of string array elements obtained
 */
int bJsonPathArrayOfPointersGetStringArray(const cJSON *object, const char *path, char *strings[], int stringNums, int stringSize);

/*****************************************************************************************************
 * Update the item
*****************************************************************************************************/
/**
 * @brief  Update the item;
 * @param  object: the cJSON object;
 * @param  path: the item path;
 * @return return 0 for sucess, -1 is failed;
 */
int bJsonPathUpdate(cJSON *object, const char *path, cJSON *newItem);

/**
 * @brief  update the double object value;
 * @param  object: the cJSON object;
 * @param  path: the item path;
 * @param  number: The updated value;
 * @return return 0 for sucess, -1 is failed;
 */
int bJsonPathUpdateDouble(cJSON *object, const char *path, double number);

/**
 * @brief  update the float object value;
 * @param  object: the cJSON object;
 * @param  path: the item path;
 * @param  number: The updated value;
 * @return return 0 for sucess, -1 is failed;
 */
int bJsonPathUpdateFloat(cJSON *object, const char *path, float number);

/**
 * @brief  update the integer object value;
 * @param  object: the cJSON object;
 * @param  path: the item path;
 * @param  number: The updated value;
 * @return return 0 for sucess, -1 is failed;
 */
int bJsonPathUpdateInteger(cJSON *object, const char *path, int number);

/**
 * @brief  update the string object value;
 * @param  object: the cJSON object;
 * @param  path: the item path;
 * @param  buffer: The updated value;
 * @return return 0 for sucess, -1 is failed;
 */
int bJsonPathUpdateString(cJSON *object, const char *path, char *buffer); 

/*****************************************************************************************************
 * Set the item
*****************************************************************************************************/
/**
 * @brief  Set the item;
 * @param  object: the cJSON object;
 * @param  path: the item path;
 * @return return 0 for sucess, -1 is failed;
 */
int bJsonPathSet(cJSON **object, const char *path, cJSON *newItem);

/**
 * @brief  set the double object value;
 * @param  object: the cJSON object;
 * @param  path: the item path;
 * @param  number: The updated value;
 * @return return 0 for sucess, -1 is failed;
 */
int bJsonPathSetDouble(cJSON **object, const char *path, double number);

/**
 * @brief  set the float object value;
 * @param  object: the cJSON object;
 * @param  path: the item path;
 * @param  number: The updated value;
 * @return return 0 for sucess, -1 is failed;
 */
int bJsonPathSetFloat(cJSON **object, const char *path, float number);

/**
 * @brief  set the integer object value;
 * @param  object: the cJSON object;
 * @param  path: the item path;
 * @param  number: The updated value;
 * @return return 0 for sucess, -1 is failed;
 */
int bJsonPathSetInteger(cJSON **object, const char *path, int number);

/**
 * @brief  set the string object value;
 * @param  object: the cJSON object;
 * @param  path: the item path;
 * @param  number: The updated value;
 * @return return 0 for sucess, -1 is failed;
 */
int bJsonPathSetString(cJSON **object, const char *path, char *buffer); 

/**
 * @brief  set the double array object value;
 * @param  object: the cJSON object;
 * @param  path: the item path;
 * @param  numbers: Points to the double array;;
 * @param  counts: how many size for double array;
 * @return return 0 for sucess, -1 is failed;
 */
int bJsonPathSetDoubleArray(cJSON **object, const char *path, double *numbers, int counts);

/**
 * @brief  set the float array object value;
 * @param  object: the cJSON object;
 * @param  path: the item path;
 * @param  numbers: Points to the float array;;
 * @param  counts: how many size for float array;
 * @return return 0 for sucess, -1 is failed;
 */
int bJsonPathSetFloatArray(cJSON **object, const char *path, float *numbers, int counts);

/**
 * @brief  set the integer array object value;
 * @param  object: the cJSON object;
 * @param  path: the item path;
 * @param  numbers: Points to the integer array;;
 * @param  counts: how many size for integer array;
 * @return return 0 for sucess, -1 is failed;
 */
int bJsonPathSetIntegerArray(cJSON **object, const char *path, int *numbers, int counts);

/**
 * @brief  Set the string array object value
 * @param  object: the cJSON object;
 * @param  path: the item path;
 * @param  strings: Points to the string array(A two-dimensional array of char types represents a string array)
 * @param  rows: Maximum number of strings (number of rows in a two-dimensional data)
 * @param  clows: Maximum length of the string (number of columns in two-dimensional data)
 * @return The number of string array elements obtained
 */
int bJsonPath2dArraySetStringArray(cJSON **object, const char *path, char *strings, int rows, int clos);

/**
 * @brief  set the string array object value
 * @param  object: the cJSON object;
 * @param  path: the item path;
 * @param  strings: Points to the string array("Strings" are arrays of pointers)
 * @param  stringNums: Number of strings
 * @param  stringSize: The maximum length of the "strings[n]"
 * @return The number of string array elements obtained
 */
int bJsonPathArrayOfPointersSetStringArray(cJSON **object, const char *path, char *strings[], int stringNums, int stringSize);


/*****************************************************************************************************
 * Delete, separate, and copy item
*****************************************************************************************************/
/**
 * @brief  Set the item;
 * @param  object: the cJSON object;
 * @param  path: the item path;
 * @return return 0 for sucess, -1 is failed, 1 is not exist
 */
int bJsonPathDel(cJSON *object, const char *path);

/**
 * @brief  detach the item;
 * @param  object: the cJSON object;
 * @param  path: the item path;
 * @return Return null if failed, otherwise point to item;
 */
cJSON *bJsonPathDetach(cJSON *object, const char *path);

/**
 * @brief   Duplicate will create a new, identical cJSON item to the one you pass, in new memory that will
 *          need to be released. With recurse!=0, it will duplicate any children connected to the item.
 *          The item->next and ->prev pointers are always zero on return from Duplicate.
 * @param   object: the cJSON object;
 * @param   path: the item path;
 * @param   recurese: 
 * @return  Return null if failed, otherwise point to duplicate item;
 */
cJSON *bJsonPathDuplicate(const cJSON *object, const char *path, bool recurese);


/*****************************************************************************************************
 * Test objects
*****************************************************************************************************/
/**
 * @brief  Check whether the item exists;
 * @param  object: the cJSON object;
 * @param  path: the item path;
 * @return return true is exist, otherwise false;
 */
bool bJsonPathIsExist(const cJSON *object, const char *path);

#ifdef __cplusplus
}
#endif /* __cplusplus */

#endif /* __B_JSON_PATH_H__ */