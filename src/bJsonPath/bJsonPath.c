/*******************************************************************************
* ZHT Communication & technology Co.Ltd 2019-2029 Copyright reserved
* File : bJsonPath.h
* Function : This is a light wrapper for cJSON lib interface.
    Provides path support for operations on cJSON objects
* Author : wanghy
* Created on: 2025.07.14
*
* Modification history:
* Ver Who Date Changes
* ------------------------------------------------------------------------------
* V1.0 wagnhy 2025/07/14 First release
********************************************************************************/
#include "bJsonPath.h"

typedef struct {
    const cJSON *node;
    char path[BJSON_PATH_MAX];
}bJsonPathFoundNode_t;  // Used to store the found nodes and their paths

/**
 * @brief   key name is numeric?;
 * @param   key: key name
 * @return  return 1 means digital, 0 means non-digital;
 */
static int bJsonPathKeyIsDigitString(const char *key)
{
    int idx = 0;
    while('\0' != key[idx]){
        if(!(key[idx] >= '0' && key[idx++] <= '9')){
            return(0);
        }
    }
    return(1);
}

/* 
 * @brief   the partition path is the individual components
 * @param   path: json object path
 * @param   count: path partitioning results array size
 * @return  return path partitioning results
 * @note    the caller releases the return value
 */
static char** bJsonPathSplitPath(const char* path, int* count) {
    if (path == NULL || strlen(path) == 0) {
        *count = 0;
        return NULL;
    }

    int segments = 1;
    const char* p = path;
    for (; *p; p++) {
        if (*p == '.') segments++;
    }

    char** result = (char**)malloc(segments * sizeof(char*));
    if (!result) return NULL;

    char* copy = strdup(path);
    if (!copy) {
        free(result);
        return NULL;
    }

    int i = 0;
    char *savePtr = NULL;
    char* token = strtok_r(copy, ".", &savePtr);
    while (token != NULL) {
        result[i++] = strdup(token);
        token = strtok_r(NULL, ".", &savePtr);
    }

    free(copy);
    *count = segments;
    return result;
}

/* 
 * @brief   release the split path memory
 * @param   path: Storage path array 
 * @param   count: The number of elements in the path array
 */
static void bJsonPathSplitPahtFree(char** path, int count) {
    if (path == NULL){
        return;
    }

    int i = 0;
    for (i = 0; i < count; i++) {
        free(path[i]);
        path[i] = NULL;
    }
    free(path);
}

/* 
 * @brief       Search target objects
 * @param       root: An ancestor object of the json type
 * @param       target: The queried json object
 * @param       results: Query the result information of "target"
 * @param       currentPath: Current path
 * @return      return -1 for exist, 0 for no exist
 */
static int bJsonSearchTarget(const cJSON *root, const cJSON *target, bJsonPathFoundNode_t *results, char *currentPath)
{
    if (!root || !target) {
        return(0);
    }

    /* Check whether the current node is the target node */
    if (target == root) {
        results->node = root;
        strncpy(results->path, currentPath, BJSON_PATH_MAX - 1);
        return(1);
    }

    int rc = 0;
    if (root->type == cJSON_Object) {
        cJSON *child = root->child;
        while (child) {
            char newPath[BJSON_PATH_MAX];
            if (strlen(currentPath) == 0) {
                snprintf(newPath, BJSON_PATH_MAX, "%s", child->string);
            } else {
                snprintf(newPath, BJSON_PATH_MAX, "%s.%s", currentPath, child->string);
            }
            rc = bJsonSearchTarget(child, target, results, newPath);
            child = child->next;
        }
    } else if (root->type == cJSON_Array) {
        cJSON *child = root->child;
        int index = 0;
        while (child) {
            char newPath[BJSON_PATH_MAX];
            snprintf(newPath, BJSON_PATH_MAX, "%s.%d", currentPath, index);
            rc = bJsonSearchTarget(child, target, results, newPath);
            child = child->next;
            index++;
        }
    }
    return(rc);
}

/*
 * @descp           Update nodes.  If a node exists in the tree, replace it; if not, add it
 * @param           root: An ancestor object of the json type
 * @param           path: Node paths
 * @param           value: The node that is set
 * @param           force: true true means mandatory.  The original json tree is not followed
 *                         false means easy.  Strictly follow the original json tree
 * @return          Return -1 for failure, other success(0 for replacing nodes, and 1 for adding)
 */
static int bJsonPathUpdateNode(cJSON** root, const char *path, cJSON *value, bool force)
{
    // Partition paths
    int pathCount = 0;
    char** pathSegments = bJsonPathSplitPath(path, &pathCount);
    if (pathSegments == NULL || pathCount == 0) {
        goto err;
    }

    if (root == NULL || path == NULL){
        goto err;
    }

    // Handle empty paths (set root nodes)
    if(false == force){
        if(0 == strlen(path) || NULL == *root){
            goto err;
        }
    }else{
        if (strlen(path) == 0) {
            cJSON_Delete(*root);
            *root = value;
            return 0;
        }
    }

    cJSON* current = *root;
    cJSON* parent = NULL;
    const char* lastKey = NULL;
    int lastIndex = 0;
    int isArray = 0;

    // If the root node does not exist, decide whether to create an object or an array based on the first paragraph
    if (current == NULL) {
        if(false == force){
            goto err;
        }
        if (bJsonPathKeyIsDigitString(pathSegments[0])) {
            current = cJSON_CreateArray();
        } else {
            current = cJSON_CreateObject();
        }
        *root = current;
    }

    // traversal path (except the last segment)
    int i = 0;
    for (; i < pathCount - 1; i++) {
        const char* segment = pathSegments[i];
        if (cJSON_IsObject(current)) {
            // Check whether the next paragraph is a number and decide whether to create an array or an object
            int shouldCreateArray = bJsonPathKeyIsDigitString(pathSegments[i+1]);
            cJSON* next = cJSON_GetObjectItem(current, segment);
            if (next == NULL) {
                next = shouldCreateArray ? cJSON_CreateArray() : cJSON_CreateObject();
                cJSON_AddItemToObject(current, segment, next);
            } else if ((shouldCreateArray && !cJSON_IsArray(next)) || 
                      (!shouldCreateArray && !cJSON_IsObject(next))) {
                if(false == force){
                    goto err;
                }
                // The type does not match and needs to be replaced
                cJSON* new_node = shouldCreateArray ? cJSON_CreateArray() : cJSON_CreateObject();
                cJSON_ReplaceItemInObject(current, segment, new_node);
                next = new_node;
            }
            
            parent = current;
            current = next;
            lastKey = segment;
            isArray = 0;
        } else if (cJSON_IsArray(current)) {
            if (!bJsonPathKeyIsDigitString(segment)) {
                bJsonPathSplitPahtFree(pathSegments, pathCount);
                goto err;
            }
            
            int index = atoi(segment);
            if (index < 0) {
                bJsonPathSplitPahtFree(pathSegments, pathCount);
                goto err;
            }

            int arraySize = cJSON_GetArraySize(current);
            if (index >= arraySize) {
                int i = arraySize;
                for (; i <= index; i++) {
                    cJSON_AddItemToArray(current, cJSON_CreateNull());
                }
            }

            cJSON* next = cJSON_GetArrayItem(current, index);
            if (cJSON_IsNull(next)) {
                // Check whether the next paragraph is a number and decide whether to create an array or an object
                int shouldCreateArray = bJsonPathKeyIsDigitString(pathSegments[i+1]);
                next = shouldCreateArray ? cJSON_CreateArray() : cJSON_CreateObject();
                cJSON_ReplaceItemInArray(current, index, next);
            }
            
            parent = current;
            current = next;
            lastIndex = index;
            isArray = 1;
        } else {
            if(false == force){
                goto err;
            }

            // The current node is neither an object nor an array and needs to be replaced
            if (parent != NULL) {
                if (isArray) {
                    // Based on the next paragraph, decide whether to create an array or an object
                    int shouldCreateArray =  bJsonPathKeyIsDigitString(pathSegments[i+1]);
                    cJSON* new_node = shouldCreateArray ? cJSON_CreateArray() : cJSON_CreateObject();
                    cJSON_ReplaceItemInArray(parent, lastIndex, new_node);
                    current = new_node;
                    i--; // Redo the current section
                    continue;
                } else {
                    // Based on the next paragraph, decide whether to create an array or an object
                    int shouldCreateArray = bJsonPathKeyIsDigitString(pathSegments[i+1]);
                    cJSON* new_node = shouldCreateArray ? cJSON_CreateArray() : cJSON_CreateObject();
                    cJSON_ReplaceItemInObject(parent, lastKey, new_node);
                    current = new_node;
                    i--; // Redo the current section
                    continue;
                }
            } else {
                // If there is no parent node, replace the root node
                cJSON_Delete(*root);
                if (bJsonPathKeyIsDigitString(pathSegments[0])) {
                    *root = cJSON_CreateArray();
                } else {
                    *root = cJSON_CreateObject();
                }
                current = *root;
                i = -1; // Start over from the first paragraph
                continue;
            }
        }
    }

    // Set the final value
    const char* lastSegment = pathSegments[pathCount - 1];
    int result = 0;
    if (cJSON_IsObject(current)) {
        if(false == force){
            cJSON *item = cJSON_GetObjectItem(current, lastSegment);
            if(NULL == item || item->type != value->type){
                goto err;
            }
        }
        // Remove existing items with the same name first (if any)
        cJSON_DeleteItemFromObject(current, lastSegment);
        cJSON_AddItemToObject(current, lastSegment, value);
    } else if (cJSON_IsArray(current)) {
        if (!bJsonPathKeyIsDigitString(lastSegment)) {
            result = -1;
        } else {
            int index = atoi(lastSegment);
            if (index < 0) {
                result = -1;
            } else {
                if(false == force){
                    cJSON *item = cJSON_GetArrayItem(current, index);
                    if(NULL == item || item->type != value->type){
                        goto err;
                    }
                }else{
                    // Make sure the array is large enough
                    int arraySize = cJSON_GetArraySize(current);
                    if (index >= arraySize) {
                        int i = arraySize;
                        for (; i <= index; i++) {
                            cJSON_AddItemToArray(current, cJSON_CreateNull());
                        }
                    }
                }
                cJSON_ReplaceItemInArray(current, index, value);
            }
        }
    } else {
        if(false == force){
            goto err;
        }

        // The current node is neither an object nor an array and needs to be replaced
        if (parent != NULL) {
            if (isArray) {
                cJSON* newContainer = bJsonPathKeyIsDigitString(lastSegment) ? 
                    cJSON_CreateArray() : cJSON_CreateObject();
                cJSON_ReplaceItemInArray(parent, lastIndex, newContainer);
                if (cJSON_IsArray(newContainer)) {
                    int index = atoi(lastSegment);
                    // Make sure the array is large enough
                    int arraySize = cJSON_GetArraySize(newContainer);
                    if (index >= arraySize) {
                        int i = arraySize;
                        for (; i <= index; i++) {
                            cJSON_AddItemToArray(newContainer, cJSON_CreateNull());
                        }
                    }
                    cJSON_ReplaceItemInArray(newContainer, index, value);
                } else {
                    cJSON_AddItemToObject(newContainer, lastSegment, value);
                }
            } else {
                cJSON* newContainer = bJsonPathKeyIsDigitString(lastSegment) ? 
                    cJSON_CreateArray() : cJSON_CreateObject();
                cJSON_ReplaceItemInObject(parent, lastKey, newContainer);
                if (cJSON_IsArray(newContainer)) {
                    int index = atoi(lastSegment);
                    // Make sure the array is large enough
                    int arraySize = cJSON_GetArraySize(newContainer);
                    if (index >= arraySize) {
                        int i = arraySize;
                        for (; i <= index; i++) {
                            cJSON_AddItemToArray(newContainer, cJSON_CreateNull());
                        }
                    }
                    cJSON_ReplaceItemInArray(newContainer, index, value);
                } else {
                    cJSON_AddItemToObject(newContainer, lastSegment, value);
                }
            }
        } else {
            // If there is no parent node, replace the root node
            cJSON_Delete(*root);
            if (bJsonPathKeyIsDigitString(lastSegment)) {
                *root = cJSON_CreateArray();
                int index = atoi(lastSegment);
                // Make sure the array is large enough
                int i = 0;
                for (; i <= index; i++) {
                    cJSON_AddItemToArray(*root, i == index ? value : cJSON_CreateNull());
                }
            } else {
                *root = cJSON_CreateObject();
                cJSON_AddItemToObject(*root, lastSegment, value);
            }
        }
    }

    bJsonPathSplitPahtFree(pathSegments, pathCount);
    if(-1 == result){
        cJSON_Delete(value);
    }
    return result;
 err:
    bJsonPathSplitPahtFree(pathSegments, pathCount);
    cJSON_Delete(value);
    return(-1);
}

/*****************************************************************************************************
 * Get the object
*****************************************************************************************************/
/**
 * @brief  Get the item;
 * @param  object: the cJSON object;
 * @param  path: the item path;
 * @return Return null if item does not exist, otherwise point to item;
 */
cJSON *bJsonPathGet(const cJSON *object, const char *path)
{
    int pathCount = 0;
    char** pathSegments = bJsonPathSplitPath(path, &pathCount);
    if (pathSegments == NULL || pathCount == 0) {
        return(NULL);
    }

    int i = 0;
    const cJSON *parentNode = object;
    const cJSON *currentNode = NULL;
    for(i=0; i<pathCount; i++){
        const char* segment = pathSegments[i];
        if (1 == bJsonPathKeyIsDigitString(segment)) {
            if(!cJSON_IsArray(parentNode)){    /* 节点类型不是数组 */
                bJsonPathSplitPahtFree(pathSegments, pathCount);
                return(NULL);
            }
            currentNode = cJSON_GetArrayItem(parentNode, atoi(segment));
        }else{
            currentNode = cJSON_GetObjectItemCaseSensitive(parentNode, segment);
        }
        parentNode = currentNode;
    }
    bJsonPathSplitPahtFree(pathSegments, pathCount);
    return((cJSON*)currentNode);
}

/**
 * @brief  Get the size of an array item;
 * @param  object: the cJSON object;
 * @param  path: the item path;
 * @return Returns the number of elements in the array;
 */
int bJsonPathGetArraySize(const cJSON *object, const char *path)
{
    const cJSON *item = bJsonPathGet(object, path);
    if(NULL != item && cJSON_Array == item->type){
        return(cJSON_GetArraySize(item));
    }
    return(0);
}

/**
 * @brief  Get the double object value
 * @param  object: the cJSON object;
 * @param  path: the item path;
 * @param  number: Points to the double;
 * @return return 0 for sucess, -1 is failed;
 */
int bJsonPathGetDouble(const cJSON *object, const char *path, double *number)
{
    const cJSON *item = bJsonPathGet(object, path);
    if(NULL != item && cJSON_Number == item->type){
        *number = cJSON_GetNumberValue(item);
        return(0);
    }
    return(-1);
}

/**
 * @brief  Get the float object value
 * @param  object: the cJSON object;
 * @param  path: the item path;
 * @param  number: Points to the float;
 * @return return 0 for sucess, -1 is failed;
 */
int bJsonPathGetFloat(const cJSON *object, const char *path, float *number)
{
    const cJSON *item = bJsonPathGet(object, path);
    if(NULL != item && cJSON_Number == item->type){
        *number = cJSON_GetNumberValue(item);
        return(0);
    }
    return(-1);
}

/**
 * @brief  Get the integer object value
 * @param  object: the cJSON object;
 * @param  path: the item path;
 * @param  number: Points to the integer;
 * @return return 0 for sucess, -1 is failed;
 */
int bJsonPathGetInteger(const cJSON *object, const char *path, int *number)
{
    const cJSON *item = bJsonPathGet(object, path);
    if(NULL != item && cJSON_Number == item->type){
        *number = cJSON_GetNumberValue(item);
        return(0);
    }
    return(-1);
}

/**
 * @brief  Get the string object value
 * @param  object: the cJSON object;
 * @param  path: the item path;
 * @param  buffer: read value will stored here;
 * @param  bufferSize: the "buffer" size;
 * @return return 0 for sucess, -1 is failed;
 */
int bJsonPathGetString(const cJSON *object, const char *path, char *buffer, int bufferSize)
{
    if(NULL == buffer || bufferSize < 1){
        return(-1);
    }

    const cJSON *item = bJsonPathGet(object, path);
    if(NULL != item && cJSON_String == item->type){
        memset(buffer, 0, bufferSize);
        strncpy(buffer, cJSON_GetStringValue(item), bufferSize-1);
        return(0);
    }
    return(-1);
}

/**
 * @brief  Get the double array object value
 * @param  object: the cJSON object;
 * @param  path: the item path;
 * @param  numbers: Points to the double array;
 * @param  counts: how many size for double array;
 * @return The number of double array elements obtained;
 */
int bJsonPathGetDoubleArray(const cJSON *object, const char *path, double *numbers, int count)
{
    int total = 0;
    const cJSON *item = bJsonPathGet(object, path);
    if(NULL != item && cJSON_Array == item->type){
        int i = 0;
        int arrSize = cJSON_GetArraySize(item);
        for(i=0; i<arrSize; i++){
            const cJSON *tail = cJSON_GetArrayItem(item, i);
            if(NULL == tail){
                continue;
            }else if(cJSON_Number == tail->type){
                if(total >= count){
                    return(total);
                }
                numbers[total++] = cJSON_GetNumberValue(tail);
            }
        }
    }
    return(total);
}

/**
 * @brief  Get the float array object value
 * @param  object: the cJSON object;
 * @param  path: the item path;
 * @param  numbers: Points to the float array;
 * @param  counts: how many size for float array;
 * @return The number of float array elements obtained;
 */
int bJsonPathGetFloatArray(const cJSON *object, const char *path, float *numbers, int count)
{
    int total = 0;
    const cJSON *item = bJsonPathGet(object, path);
    if(NULL != item && cJSON_Array == item->type){
        int i = 0;
        int arrSize = cJSON_GetArraySize(item);
        for(i=0; i<arrSize; i++){
            const cJSON *tail = cJSON_GetArrayItem(item, i);
            if(NULL == tail){
                continue;
            }else if(cJSON_Number == tail->type){
                if(total >= count){
                    return(total);
                }
                numbers[total++] = cJSON_GetNumberValue(tail);
            }
        }
    }
    return(total);
}

/**
 * @brief  Get the integer array object value
 * @param  object: the cJSON object;
 * @param  path: the item path;
 * @param  numbers: Points to the interger array;
 * @param  counts: how many size for interger array;
 * @return The number of interger array elements obtained;
 */
int bJsonPathGetIntegerArray(const cJSON *object, const char *path, int *numbers, int count)
{
    int total = 0;
    const cJSON *item = bJsonPathGet(object, path);
    if(NULL != item && cJSON_Array == item->type){
        int i = 0;
        int arrSize = cJSON_GetArraySize(item);
        for(i=0; i<arrSize; i++){
            const cJSON *tail = cJSON_GetArrayItem(item, i);
            if(NULL == tail){
                continue;
            }else if(cJSON_Number == tail->type){
                if(total >= count){
                    return(total);
                }
                numbers[total++] = cJSON_GetNumberValue(tail);
            }
        }
    }
    return(total);
}

/**
 * @brief  Get the string array object value
 * @param  object: the cJSON object;
 * @param  path: the item path;
 * @param  strings: Points to the string array(A two-dimensional array of char types represents a string array)
 * @param  rows: Maximum number of strings (number of rows in a two-dimensional data)
 * @param  clows: Maximum length of the string (number of columns in two-dimensional data)
 * @return The number of string array elements obtained
 */
int bJsonPath2dArrayGetStringArray(const cJSON *object, const char *path, char *strings, int rows, int clos)
{
    if(NULL == strings || rows <= 0 || clos <= 1){
        return(0);
    }

    int total = 0;
    const cJSON *item = bJsonPathGet(object, path);
    if(NULL != item && cJSON_Array == item->type){
        int i = 0;
        int arrSize = cJSON_GetArraySize(item);

        memset(strings, 0, rows*clos);
        for(i=0; i<arrSize; i++){
            const cJSON *tail = cJSON_GetArrayItem(item, i);
            if(NULL == tail){
                continue;
            }else if(cJSON_String == tail->type){
                if(total >= rows){
                    return(total);
                }
                strncpy(strings + total*clos, cJSON_GetStringValue(tail), clos-1);
                total++;
            }
        }
    }
    return(total);
}

/**
 * @brief  Get the string array object value
 * @param  object: the cJSON object;
 * @param  path: the item path;
 * @param  strings: Points to the string array("Strings" are arrays of pointers)
 * @param  stringNums: Number of strings
 * @param  stringSize: The maximum length of the "strings[n]"
 * @return The number of string array elements obtained
 */
int bJsonPathArrayOfPointersGetStringArray(const cJSON *object, const char *path, char *strings[], int stringNums, int stringSize)
{
    int total = 0;
    const cJSON *item = bJsonPathGet(object, path);
    if(NULL != item && cJSON_Array == item->type){
        int i = 0;
        int arrSize = cJSON_GetArraySize(item);
        for(i=0; i<arrSize; i++){
            const cJSON *tail = cJSON_GetArrayItem(item, i);
            if(NULL == tail){
                continue;
            }else if(cJSON_String == tail->type){
                if(total >= stringNums){
                    return(total);
                }
                memset(strings[total], 0, stringSize);
                strncpy(strings[total], cJSON_GetStringValue(tail), stringSize-1);
                total++;
            }
        }
    }
    return(total);
}

/*****************************************************************************************************
 * Update the item
*****************************************************************************************************/
/**
 * @brief  Update the item;
 * @param  object: the cJSON object;
 * @param  path: the item path;
 * @return return 0 for sucess, -1 is failed;
 */
int bJsonPathUpdate(cJSON *object, const char *path, cJSON *newItem)
{
    return(bJsonPathUpdateNode(&object, path, newItem, false));
}

/**
 * @brief  update the double object value;
 * @param  object: the cJSON object;
 * @param  path: the item path;
 * @param  number: The updated value;
 * @return return 0 for sucess, -1 is failed;
 */
int bJsonPathUpdateDouble(cJSON *object, const char *path, double number)
{
    return(bJsonPathUpdateNode(&object, path, cJSON_CreateNumber(number), false));
}

/**
 * @brief  update the float object value;
 * @param  object: the cJSON object;
 * @param  path: the item path;
 * @param  number: The updated value;
 * @return return 0 for sucess, -1 is failed;
 */
int bJsonPathUpdateFloat(cJSON *object, const char *path, float number)
{
    return(bJsonPathUpdateNode(&object, path, cJSON_CreateNumber(number), false));
}

/**
 * @brief  update the integer object value;
 * @param  object: the cJSON object;
 * @param  path: the item path;
 * @param  number: The updated value;
 * @return return 0 for sucess, -1 is failed;
 */
int bJsonPathUpdateInteger(cJSON *object, const char *path, int number)
{
    return(bJsonPathUpdateNode(&object, path, cJSON_CreateNumber(number), false));
}

/**
 * @brief  update the string object value;
 * @param  object: the cJSON object;
 * @param  path: the item path;
 * @param  buffer: The updated value;
 * @return return 0 for sucess, -1 is failed;
 */
int bJsonPathUpdateString(cJSON *object, const char *path, char *buffer)
{
    return(bJsonPathUpdateNode(&object, path, cJSON_CreateString(buffer), false));
}

/*****************************************************************************************************
 * Set the item
*****************************************************************************************************/
/**
 * @brief  Set the item;
 * @param  object: the cJSON object;
 * @param  path: the item path;
 * @return return 0 for sucess, -1 is failed;
 */
int bJsonPathSet(cJSON **object, const char *path, cJSON *newItem)
{
    return(bJsonPathUpdateNode(object, path, newItem, true));
}

/**
 * @brief  set the double object value;
 * @param  object: the cJSON object;
 * @param  path: the item path;
 * @param  number: The updated value;
 * @return return 0 for sucess, -1 is failed;
 */
int bJsonPathSetDouble(cJSON **object, const char *path, double number)
{
    return(bJsonPathUpdateNode(object, path, cJSON_CreateNumber(number), true));
}

/**
 * @brief  set the float object value;
 * @param  object: the cJSON object;
 * @param  path: the item path;
 * @param  number: The updated value;
 * @return return 0 for sucess, -1 is failed;
 */
int bJsonPathSetFloat(cJSON **object, const char *path, float number)
{
    return(bJsonPathUpdateNode(object, path, cJSON_CreateNumber(number), true));
}

/**
 * @brief  set the integer object value;
 * @param  object: the cJSON object;
 * @param  path: the item path;
 * @param  number: The updated value;
 * @return return 0 for sucess, -1 is failed;
 */
int bJsonPathSetInteger(cJSON **object, const char *path, int number)
{
    return(bJsonPathUpdateNode(object, path, cJSON_CreateNumber(number), true));
}

/**
 * @brief  set the string object value;
 * @param  object: the cJSON object;
 * @param  path: the item path;
 * @param  number: The updated value;
 * @return return 0 for sucess, -1 is failed;
 */
int bJsonPathSetString(cJSON **object, const char *path, char *buffer)
{
    return(bJsonPathUpdateNode(object, path, cJSON_CreateString(buffer), true));
}

/**
 * @brief  set the double array object value;
 * @param  object: the cJSON object;
 * @param  path: the item path;
 * @param  numbers: Points to the double array;;
 * @param  counts: how many size for double array;
 * @return return 0 for sucess, -1 is failed;
 */
int bJsonPathSetDoubleArray(cJSON **object, const char *path, double *numbers, int counts)
{
    int i = 0;
    cJSON *doubleArr = cJSON_CreateArray();
    for(i=0; i<counts; i++){
        cJSON_AddItemToArray(doubleArr, cJSON_CreateNumber(numbers[i]));
    }
    return(bJsonPathUpdateNode(object, path, doubleArr, true));
}

/**
 * @brief  set the float array object value;
 * @param  object: the cJSON object;
 * @param  path: the item path;
 * @param  numbers: Points to the float array;;
 * @param  counts: how many size for float array;
 * @return return 0 for sucess, -1 is failed;
 */
int bJsonPathSetFloatArray(cJSON **object, const char *path, float *numbers, int counts)
{
    int i = 0;
    cJSON *floatArr = cJSON_CreateArray();
    for(i=0; i<counts; i++){
        cJSON_AddItemToArray(floatArr, cJSON_CreateNumber(numbers[i]));
    }
    return(bJsonPathUpdateNode(object, path, floatArr, true));
}

/**
 * @brief  set the integer array object value;
 * @param  object: the cJSON object;
 * @param  path: the item path;
 * @param  numbers: Points to the integer array;;
 * @param  counts: how many size for integer array;
 * @return return 0 for sucess, -1 is failed;
 */
int bJsonPathSetIntegerArray(cJSON **object, const char *path, int *numbers, int counts)
{
    int i = 0;
    cJSON *integerArr = cJSON_CreateArray();
    for(i=0; i<counts; i++){
        cJSON_AddItemToArray(integerArr, cJSON_CreateNumber(numbers[i]));
    }
    return(bJsonPathUpdateNode(object, path, integerArr, true));
}

/**
 * @brief  Set the string array object value
 * @param  object: the cJSON object;
 * @param  path: the item path;
 * @param  strings: Points to the string array(A two-dimensional array of char types represents a string array)
 * @param  rows: Maximum number of strings (number of rows in a two-dimensional data)
 * @param  clows: Maximum length of the string (number of columns in two-dimensional data)
 * @return The number of string array elements obtained
 */
int bJsonPath2dArraySetStringArray(cJSON **object, const char *path, char *strings, int rows, int clos)
{
    int total = 0;
    cJSON *stringArr = cJSON_CreateArray();
    if(clos > 1){
        char *buffer = malloc(clos);
        if(NULL == buffer){
            return(total);
        }

        int i = 0;
        for(i=0; i<rows; i++){
            memset(buffer, 0, clos);
            strncpy(buffer, strings + i*clos, clos-1);
            cJSON_AddItemToArray(stringArr, cJSON_CreateString(buffer));
            total++;
        }
        free(buffer);
    }
    if(0 == bJsonPathUpdateNode(object, path, stringArr, true)){
        return(total);
    }
    return(0);
}

/**
 * @brief  set the string array object value
 * @param  object: the cJSON object;
 * @param  path: the item path;
 * @param  strings: Points to the string array("Strings" are arrays of pointers)
 * @param  stringNums: Number of strings
 * @param  stringSize: The maximum length of the "strings[n]"
 * @return The number of string array elements obtained
 */
int bJsonPathArrayOfPointersSetStringArray(cJSON **object, const char *path, char *strings[], int stringNums, int stringSize)
{
    int total = 0;
    cJSON *stringArr = cJSON_CreateArray();
    if(stringSize > 1){
        int i = 0;
        for(i=0; i<stringNums; i++){
            cJSON_AddItemToArray(stringArr, cJSON_CreateString(strings[i]));
            total++;
        }
    }
    if(0 == bJsonPathUpdateNode(object, path, stringArr, true)){
        return(total);
    }
    return(0);
}

/*****************************************************************************************************
 * Delete, separate, and copy item
*****************************************************************************************************/
/**
 * @brief  Set the item;
 * @param  object: the cJSON object;
 * @param  path: the item path;
 * @return return 0 for sucess, -1 is failed, 1 is not exist
 * @note   
 */
int bJsonPathDel(cJSON *object, const char *path)
{
    if(NULL == path || strlen(path) <= 0){
        cJSON_Delete(object);
        return(0);
    }

    cJSON *parent = NULL;
    char parentKey[256];
    char *lastDot = strrchr(path, '.');
    if(NULL != lastDot){
        int parentKeyLen = lastDot - path;
        if(parentKeyLen >= sizeof(parentKey)){
            return(-1);
        }
        strncpy(parentKey, path, parentKeyLen);
        parentKey[parentKeyLen] = '\0';
        if(parentKeyLen <= 0){
            parent = object;
        }else{
            parent = bJsonPathGet(object, parentKey);
        }
    }else{
        parentKey[0] = '\0';
        parent = object;
    }
    cJSON *self = bJsonPathGet(object, path);
    if(NULL == self){
        return(1);
    }
    cJSON_Delete(cJSON_DetachItemViaPointer(parent, self));
    return(0);
}

/**
 * @brief  detach the item;
 * @param  object: the cJSON object;
 * @param  path: the item path;
 * @return Return null if failed, otherwise point to item;
 */
cJSON *bJsonPathDetach(cJSON *object, const char *path)
{
    if(NULL == object || NULL == path){
        return(NULL);
    }

    cJSON *parent = object;
    char key[BJSON_PATH_MAX] = {0};
    char *p = strrchr(path, '.');
    if(NULL != p){  /* path指向的节点是root的孙子及以下节点 */
        strncpy(key, path, p - path);
        parent = bJsonPathGet(object, key);
    }else{  /* path指向的节点是root的子节点 */
        strncpy(key, path, BJSON_PATH_MAX);
    }
    cJSON *item = bJsonPathGet(object, path);
    return(cJSON_DetachItemViaPointer(parent, item));
}

/**
 * @brief   Duplicate will create a new, identical cJSON item to the one you pass, in new memory that will
 *          need to be released. With recurse!=0, it will duplicate any children connected to the item.
 *          The item->next and ->prev pointers are always zero on return from Duplicate.
 * @param   object: the cJSON object;
 * @param   path: the item path;
 * @param   recurese:  With recurse!=0, it will duplicate any children connected to the item.
 * @return  Return null if failed, otherwise point to duplicate item;
 */
cJSON *bJsonPathDuplicate(const cJSON *object, const char *path, bool recurese)
{
    if(NULL == path || strlen(path) <= 0){
        return(cJSON_Duplicate(object, recurese));
    }

    const cJSON *item = bJsonPathGet(object, path);
    return(cJSON_Duplicate(item, recurese));
}


/*****************************************************************************************************
 * Test objects
*****************************************************************************************************/
/**
 * @brief  Check whether the item exists;
 * @param  object: the cJSON object;
 * @param  path: the item path;
 * @return return true is exist, otherwise false;
 */
bool bJsonPathIsExist(const cJSON *object, const char *path)
{
    return(NULL == bJsonPathGet(object, path) ? false : true);
}