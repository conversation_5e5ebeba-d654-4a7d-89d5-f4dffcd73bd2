Copyright (c) 2010 <PERSON> and Contributed to Unity Project

Unity Project - A Test Framework for C
Copyright (c) 2007 <PERSON>, <PERSON>, <PERSON>
[Released under MIT License. Please refer to license.txt for details]

This Framework is an optional add-on to Unity.  By including unity_framework.h in place of unity.h,
you may now work with Unity in a manner similar to CppUTest.  This framework adds the concepts of 
test groups and gives finer control of your tests over the command line.