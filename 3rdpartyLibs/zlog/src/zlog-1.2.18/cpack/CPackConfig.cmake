SET(CPAC<PERSON>_CMAKE_GENERATOR "${CPACK_CMAKE_GENERATOR}")
SET(CPACK_GENERATOR "${CPACK_GENERATOR}")
SET(CPACK_OUTPUT_CONFIG_FILE "${CPACK_OUTPUT_CONFIG_FILE}")
SET(CPACK_INSTALL_CMAKE_PROJECTS "${CPACK_INSTALL_CMAKE_PROJECTS}")

SET(CPACK_PACKAGE_VERSION_MAJOR "${CPACK_PACKAGE_VERSION_MAJOR}")
SET(CPACK_PACKAGE_VERSION_MINOR "${CPACK_PACKAGE_VERSION_MINOR}")
SET(CPACK_PACKAGE_VERSION_PATCH "${CPACK_PACKAGE_VERSION_PATCH}")
SET(CPACK_RPM_PACKAGE_RELEASE "${CPACK_RPM_PACKAGE_RELEASE}") # release version.
SET(CPACK_PACKAGE_VERSION "${CPACK_PACKAGE_VERSION}")

SET(CPACK_PACKAGE_NAME "${CPACK_PACKAGE_NAME}")
SET(CPACK_PACKAGE_DESCRIPTION_SUMMARY "${CPACK_PACKAGE_DESCRIPTION_SUMMARY}")
SET(CPACK_PACKAGE_DESCRIPTION_FILE "${CPACK_PACKAGE_DESCRIPTION_FILE}")

SET(CPACK_PACKAGE_VENDOR "${CPACK_PACKAGE_VENDOR}")
SET(CPACK_PACKAGING_INSTALL_PREFIX "${CPACK_PACKAGING_INSTALL_PREFIX}")
SET(CPACK_PACKAGE_CONTACT "${CPACK_PACKAGE_CONTACT}")

SET(CPACK_PACKAGE_INSTALL_REGISTRY_KEY "${CPACK_PACKAGE_NAME} ${CPACK_PACKAGE_VERSION}")

SET(CPACK_SYSTEM_NAME "${CPACK_SYSTEM_NAME}")
SET(CPACK_TOPLEVEL_TAG "${CPACK_TOPLEVEL_TAG}")
SET(CPACK_PACKAGE_FILE_NAME "${CPACK_PACKAGE_NAME}-${CPACK_PACKAGE_VERSION}-${CPACK_RPM_PACKAGE_RELEASE}-${CPACK_TOPLEVEL_TAG}")

# SET(CPACK_PACKAGE_EXECUTABLES "ccmake;CMake")
# SET(CPACK_STRIP_FILES "bin/ccmake;bin/cmake;bin/cpack;bin/ctest")
set(CPACK_NSIS_MODIFY_PATH, ON)

SET(CPACK_DEBIAN_PACKAGE_ARCHITECTURE "${CPACK_DEBIAN_PACKAGE_ARCHITECTURE}")
SET(CPACK_RPM_PACKAGE_ARCHITECTURE "${CPACK_RPM_PACKAGE_ARCHITECTURE}")

SET(CPACK_RPM_PACKAGE_REQUIRES "${CPACK_RPM_PACKAGE_REQUIRES}")
SET(CPACK_DEBIAN_PACKAGE_DEPENDS "${CPACK_DEBIAN_PACKAGE_DEPENDS}")

SET(CPACK_DEBIAN_PACKAGE_CONTROL_EXTRA
        "${CMAKE_CURRENT_BINARY_DIR}/preinst;${CMAKE_CURRENT_BINARY_DIR}/postinst;${CMAKE_CURRENT_BINARY_DIR}/prerm;${CMAKE_CURRENT_BINARY_DIR}/postrm")

SET(CPACK_RPM_PRE_INSTALL_SCRIPT_FILE "${CMAKE_CURRENT_BINARY_DIR}/preinst")
SET(CPACK_RPM_POST_INSTALL_SCRIPT_FILE "${CMAKE_CURRENT_BINARY_DIR}/postinst")
SET(CPACK_RPM_PRE_UNINSTALL_SCRIPT_FILE "${CMAKE_CURRENT_BINARY_DIR}/prerm")
SET(CPACK_RPM_POST_UNINSTALL_SCRIPT_FILE "${CMAKE_CURRENT_BINARY_DIR}/postrm")

SET(CPACK_RPM_PACKAGE_LICENSE "${CPACK_RPM_PACKAGE_LICENSE}")

SET(CPACK_RPM_PACKAGE_GROUP "${CPACK_RPM_PACKAGE_GROUP}")
