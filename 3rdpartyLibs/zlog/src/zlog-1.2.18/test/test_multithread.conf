[levels]
TRACE = 10, LOG_DEBUG
SECURITY = 150, LOG_ALERT
[formats]
simple = "%d(%m%d%H%M%S).%us %-6c %-10V [%06k] %m%n"
security = "%d(%m%d%H%M%S).%us %-6c [%-8V] %m%n"
developer = "%d(%m%d%H%M%S).%us %-6c %-10V [%t] %F(%L)/%U() - %m%n"
csv = "%d.%ms;%m%n [%08t] [%T] [%06k] "
[rules]
clsn.ERROR >stdout; developer
high.ERROR >stdout; security
main.* >stdout; simple
main.* "./test_multithread-logs.txt"; simple
thrd.* "./test_multithread-logs/threadslog.csv", 20KB * 30 ~ "./test_multithread-logs/threadslog#r.csv"; csv
