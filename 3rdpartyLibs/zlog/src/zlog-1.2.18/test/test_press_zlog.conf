[global]
#default format = "%d(%F %T).%us %-6V %p:%T:%F:%L %m%n"

default format = "%d.%us %-6V %p:%T:%F:%L %m%n"

[rules]
# time ./test_press_zlog 1 10 100000				 real    user    sys

#*.*             	| /usr/bin/cronolog press%Y%m%d.log	#1.632s  2.010s  1.100s
*.*             	"press.log"				#2.364s  2.090s  2.460s
#*.*             	"press.log",10M				#4.644s  2.540s  6.260s
#*.*             	"press%d(%Y%m%d).log"			#4.132s  2.910s  5.030s
#*.*             	"press%d(%Y%m%d).log",1M*5		#4.713s  2.740s  6.310s
#*.*             	"press.%d(%F).log",1MB ~ "press.#2r.log"#4.730s  2.690s  6.360s
