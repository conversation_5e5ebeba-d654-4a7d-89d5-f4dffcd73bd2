#define test_conf2_conf "[global]\nstrict init = true\nbuffer min=		1024\nbuffer max=			2MB\nrotate lock file	=	/tmp/zlog.lock\ndefault format	=		\"defalut - %d(%F %X.%ms) %-6V (%c:%F:%U:%L) - %m%n\"\n\n[formats]\nnull	=		\"%n\"\nprint	=		\"print - [%-10.3d(%F)]%n\"\n\ndate	=		\"date start%n%d(%a--Wed)%n%d(%A--Wednesday)%n%d(%b--Mar)%n%d(%B--March)%n%d(%c--WedMar211:45:262011)%n%d(%C--20)%n%d(%d--02)%n%d(%D--03/02/11)%n%d(%e--2)%n%d(%F--2011-03-02)%n%d(%g--11)%n%d(%G--2011)%n%d(%h--Mar)%n%d(%H--11)%n%d(%I--11)%n%d(%j--061)%n%d(%k-k)%n%d(%l-l)%n%d(%ms--500)%n%d(%m--03)%n%d(%M--45)%n%d(%us--500730)%n%d(%p--AM)%n%d(%r--11:45:26AM)%n%d(%R--11:45)%n%d(%s--epoch)%n%d(%S--26)%n%d(%t--)%n%d(%T--11:45:26)%n%d(%u--3)%n%d(%U--09)%n%d(%V--09)%n%d(%w--3)%n%d(%W--09)%n%d(%x--03/02/11)%n%d(%X--11:45:26)%n%d(%y--11)%n%d(%Y--2011)%n%d(%z--+0800)%n%d(%Z--CST)%n%d(%%--%)%n%d(%J--%J)%ndate end%n\"\n\nsimple	=		\"simple - %m%n\"\n\ntext	=		\"text - text%n\"\n\nms	=		\"ms - %d(%a--Wed)[%d(%ms)]%n\"\n\nmsus	=		\"msus - %d(%ms,%us,%ms,%us)%n\"\n\n[rules]\n*.*			>stderr;\n*.*			>stderr; null\n*.*			>stderr; print\n*.*			>stderr; date\n*.*			>stderr; simple\n*.*			>stderr; text\n*.*			>stderr; ms\n*.*			>stderr; msus"
