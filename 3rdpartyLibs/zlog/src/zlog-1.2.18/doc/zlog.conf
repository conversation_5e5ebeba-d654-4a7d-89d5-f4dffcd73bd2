[global]
strict init = true
reload conf period = 10M

buffer min = 1024
buffer max = 2MB

#rotate lock file = /tmp/zlog.lock
rotate lock file = self
default format = "%d(%F %T.%l) %-6V (%c:%F:%L) - %m%n"

file perms = 600
fsync period = 1K

[levels]
TRACE = 10
CRIT = 130, LOG_CRIT

[formats]
simple = "%m%n"
normal = "%d(%F %T.%l) %m%n"

[rules]
default.*		>stdout; simple

*.*			-"%12.2E(HOME)/log/%c.log", \
			1MB * 12 ~ "%E(HOME)/log/%c.%D(%F) #2r #3s.log"; \
			simple

my_.INFO		>stderr;
my_cat.!ERROR		"aa.log"
my_dog.=DEBUG		>syslog, LOG_LOCAL0; simple
my_dog.=DEBUG		| /usr/bin/cronolog /www/logs/example_%Y%m%d.log ; normal
my_mice.*		$record_func , "record_path%c"; normal


