zlog (1.2.17) unstable; urgency=medium

  * Change License to Apache 2.0, for more people.

 -- [<PERSON>] <<EMAIL>>

zlog (1.2.16) unstable; urgency=medium

  * Minor bug fixes.

 -- [<PERSON>] <<EMAIL>>

zlog (1.2.15) unstable; urgency=medium

  * Added support for %r to insert a carriage return.
  * Minor bug fixes.

 -- [<PERSON>] <<EMAIL>>

zlog (1.2.14) unstable; urgency=medium

  * Added support for all syslog facilities.
  * Test: Added long log test.
  * Added '-' to the list of accepted characters in category name.
  * Added API to get zlog version.
  * Added %k formatter with leading 0's formatting.
  * Added API for category log level checking.
  * Fixed compile errors on some platforms.
  * Minor bug fixes.

 -- [<PERSON>] <<EMAIL>>

zlog (1.2.12) unstable; urgency=medium

  * Bugfix to avoid segmentation fault if zlog_init() is called many times.
  * Static file rule to emulate Python's WatchedFileHandler mode when used with external log rotation.

 -- [<PERSON>] <<EMAIL>>

zlog (1.2.10) unstable; urgency=medium

  * Bugfix: LGPL v3 -> LGPL v2.1.

 -- [<PERSON>] <<EMAIL>>

zlog (1.2.8) unstable; urgency=medium

  * Added gcc __attribute__ macro for checking zlog's format output.
  * Added support for # comments in configuration file.

 -- [Hardy Simpson] <<EMAIL>>

zlog (1.2.5) unstable; urgency=medium

  * Ensured automatic deletion of thread on single-thread exit using atexit.
  * Cached multiple times to ensure every %d is cached every second during strftime.
  * Removed usage of %D, although library still supports it, documentation and example code use %d.
  * Fixed makefile for static linking zlog-chk-conf.

 -- [Hardy Simpson] <<EMAIL>>

zlog (1.2.4) unstable; urgency=medium

  * No longer maintains thread_list; checks configuration for updates during each log write.
  
 -- [Hardy Simpson] <<EMAIL>>

zlog (1.2.0) unstable; urgency=medium

  * Introduced rotate and zip scheme.
  * Globalized some static variables to reduce interface parameters.
  * Added fstat64.
  * Implemented pipe, cronolog.
  * Ignored second parameter in record.
  * Cross-platform support for __VA_ARGS__.
  * Added git/version flags in code (redis).
  * Removed auto tools, using custom makefile (redis).
  * Lowercased macros.
  * Used const.
  * Introduced per-thread rule->file fd write time.
  * Used time for reload conf period calculation.
  * Added zlog_set_conf("section", "command").
  * Rewrote format, spec, level using direct arrays for improved performance.
  * Added manpage, df, examples.

 -- [Hardy Simpson] <<EMAIL>>

zlog (1.1.3) unstable; urgency=medium

  * Dynamically adjusted zlog_record.
  
 -- [Hardy Simpson] <<EMAIL>>

zlog (1.1.2) unstable; urgency=medium

  * Corrected rule.c, capped path_buf.
  
 -- [Hardy Simpson] <<EMAIL>>

zlog (1.1.1) unstable; urgency=medium

  * Corrected rule.c, avoided using FILE* to prevent interleaved logs.

 -- [Hardy Simpson] <<EMAIL>>

zlog (1.1.0) unstable; urgency=medium

  * Switched spec to switch loop, introduced bit-wise lock-free checks.
  * Established bit flags for us, srcline.
  * Added pthread_key_t for thread-private storage.
  * Added %D, cached strftime results.
  * Cached pid, tid as strings.
  * Removed rc usage (redis).
  * Cleaned up ZLOG_FMT and ZLOG_HEX.

 -- [Hardy Simpson] <<EMAIL>>

zlog (1.0.7) unstable; urgency=medium

  * Partial implementation of custom strftime for efficiency.
  * %t displayed in hexadecimal.
  * Various minor performance optimizations.

 -- [Hardy Simpson] <<EMAIL>>

zlog (1.0.6) unstable; urgency=medium

  * Obtained localtime_r every second for over 1x acceleration.

 -- [Hardy Simpson] <<EMAIL>>

zlog (1.0.5) unstable; urgency=medium

  * Fixed issue from 1.0.4; used read-write lock for reopening files.

 -- [Hardy Simpson] <<EMAIL>>

zlog (1.0.4) unstable; urgency=medium

  * Tested multi-threading efficiency.
  * Cached static file's FILE* and fd for 3x optimization.

 -- [Hardy Simpson] <<EMAIL>>

zlog (1.0.3) unstable; urgency=medium

  * Automatic configuration refresh based on log count.
  * Automatic sync to disk based on log count.

 -- [Hardy Simpson] <<EMAIL>>

zlog (1.0.2) unstable; urgency=medium

  * Added synchronous write to disk option (time-consuming when open).
  
 -- [Hardy Simpson] <<EMAIL>>

zlog (1.0.1) unstable; urgency=medium

  * Used configuration file as lock file.

 -- [Hardy Simpson] <<EMAIL>>

zlog (1.0.0) unstable; urgency=medium

  * Changed configuration file layout.
  * Made reload atomic, falling back to old on failure.
  * Optimized spec_gen_msg using custom buf_append alignment.
  * Introduced __func__.
  * Set file permissions.
  * Customizable output function.

 -- [Hardy Simpson] <<EMAIL>>

zlog (0.9) unstable; urgency=medium

  * Shifted towards an object-oriented approach.
  * Used arraylist instead of linklist as an internal data structure.
  * Improved configuration file format.
  * Created a thread-safe logging function library.
  * Resolved microsecond/millisecond representation in configuration.
  * Merged event into thread to avoid memory allocation.
  * Added init API for reading configuration files.
  * Parsed default format when initialized without configuration, wrote zlog_chk_conf.
  * Isolated category.
  * Added update API.
  * Adjusted buf size dynamically through zlog_init, rebuilt all caches using zlog_update.
  * Aligned file creation permissions similar to fopen (0666 & (~umask)).
  * Handled syslog output, though syslog was found slow.
  * Traditional mode for rotation, polling, etc.
  * Added zlog_profile API for diagnosing configuration memory.
  * Added MDC with configurable additional fields.
  * Tested buffer rewriting to ensure trailing \0.
  * Renamed xlog to zlog.
  * Retrieved ZLOG_ERROR_LOG and ZLOG_DEBUG_LOG once to avoid multiple getenv calls.
  * Closed zc_assert by default, opened for debugging.
  * Added match for !.*, matching unmatched categories.
  * Changed priority to level.
  * Customizable level.
  * Syslog open in rule.
  * Moved default format to global configuration.
  * Reset level using arraylist.
  * Introduced ylog, simplified interface, renamed to dzlog.
  * Added zlog-gen-conf.
  * Controlled rotate count.
  * Abandoned ideas:
    - [x] Used writev to write logs, improve efficiency, achieve one-time formation, multiple outputs -- writev internally also uses buffer memcpy.
    - [x] Tried semaphore method to make it thread-safe for archiving mutual exclusion problems.
    - [x] zlog.h variable-length parameter macro problem (refer to glib's log solution).
    - [x] dynamic] file in rule can be obtained from format.
    - [x] Modified arraylist read length, loop, etc.
    - [x] Specified whether to use threads during runtime.
    - [x] buf_append optimization -- for safety, did not optimize this.
    - [x] Used some automatic compiler and document generator.
    - [x] Output to pipes, named pipes same as files.
    - [x] Print stack... may need language support.
    - [x] zlog-input -c category -p priority -f conf.
    - [x] Used fine-grained reo en method to intelligently determine whether to reopen the file with fo en, the operating system itself has done well enough, no need to do.

 -- [Hardy Simpson] <<EMAIL>>


