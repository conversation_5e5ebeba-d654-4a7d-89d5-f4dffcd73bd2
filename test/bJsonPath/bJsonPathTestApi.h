/*******************************************************************************
* ZHT Communication & technology Co.Ltd 2019-2029 Copyright reserved
* File : bJsonPathTestApi.h
* Function : bJsonPath test;
* Author : wuliuzhi
* Created on: 2025.07.14
*
* Modification history:
* Ver Who Date Changes
* ------------------------------------------------------------------------------
* V1.0 wanghy 2025/07/14 First release
********************************************************************************/
#ifndef __BJSON_PATH_TEST_API_H__
#define __BJSON_PATH_TEST_API_H__

#include <unistd.h>
#include "unity.h"
#include "bJsonPath.h"

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

void test_bJsonPathGet(void);
void test_bJsonPathGetArraySize(void);
void test_bJsonPathGetDouble(void);
void test_bJsonPathGetFloat(void);
void test_bJsonPathGetInteger(void);
void test_bJsonPathGetString(void);
void test_bJsonPathGetDoubleArray(void);
void test_bJsonPathGetFloatArray(void);
void test_bJsonPathGetIntegerArray(void);
void test_bJsonPath2dArrayGetStringArray(void);
void test_bJsonPathArrayOfPointersGetStringArray(void);
void test_bJsonPathUpdate(void);
void test_bJsonPathUpdateDouble(void);
void test_bJsonPathUpdateFloat(void);
void test_bJsonPathUpdateInteger(void);
void test_bJsonPathUpdateString(void);
void test_bJsonPathSet(void);
void test_bJsonPathSetDouble(void);
void test_bJsonPathSetFloat(void);
void test_bJsonPathSetInteger(void);
void test_bJsonPathSetString(void);
void test_bJsonPathSetDoubleArray(void);
void test_bJsonPathSetFloatArray(void);
void test_bJsonPathSetIntegerArray(void);
void test_bJsonPath2dArraySetStringArray(void);
void test_bJsonPathArrayOfPointersSetStringArray(void);
void test_bJsonPathDel(void);
void test_bJsonPathDetach(void);
void test_bJsonPathDuplicate(void);
void test_bJsonPathIsExist(void);

#ifdef __cplusplus
}
#endif /* __cplusplus */

#endif /* __BJSON_PATH_TEST_API_H__ */