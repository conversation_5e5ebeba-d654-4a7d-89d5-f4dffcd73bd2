/*******************************************************************************
* ZHT Communication & technology Co.Ltd 2019-2029 Copyright reserved
* File : bJsonPathTest.c
* Function : bJsonPath test;
* Author : wanghy
* Created on: 2025.07.14
*
* Modification history:
* Ver Who Date Changes
* ------------------------------------------------------------------------------
* V1.0 wuliuzhi 2025/07/14 First release
********************************************************************************/
#include "bJsonPathTestApi.h"

int main(int argc, char *argv[])
{
    UNITY_BEGIN();

    RUN_TEST(test_bJsonPathGet);
    RUN_TEST(test_bJsonPathGetArraySize);
    RUN_TEST(test_bJsonPathGetDouble);
    RUN_TEST(test_bJsonPathGetFloat);
    RUN_TEST(test_bJsonPathGetInteger);
    RUN_TEST(test_bJsonPathGetString);
    RUN_TEST(test_bJsonPathGetDoubleArray);
    RUN_TEST(test_bJsonPathGetFloatArray);
    RUN_TEST(test_bJsonPathGetIntegerArray);
    RUN_TEST(test_bJsonPath2dArrayGetStringArray);
    RUN_TEST(test_bJsonPathArrayOfPointersGetStringArray);
    RUN_TEST(test_bJsonPathUpdate);
    RUN_TEST(test_bJsonPathUpdateDouble);
    RUN_TEST(test_bJsonPathUpdateFloat);
    RUN_TEST(test_bJsonPathUpdateInteger);
    RUN_TEST(test_bJsonPathUpdateString);
    RUN_TEST(test_bJsonPathSet);
    RUN_TEST(test_bJsonPathSetDouble);
    RUN_TEST(test_bJsonPathSetFloat);
    RUN_TEST(test_bJsonPathSetInteger);
    RUN_TEST(test_bJsonPathSetString);
    RUN_TEST(test_bJsonPathSetDoubleArray);
    RUN_TEST(test_bJsonPathSetFloatArray);
    RUN_TEST(test_bJsonPathSetIntegerArray);
    RUN_TEST(test_bJsonPath2dArraySetStringArray);
    RUN_TEST(test_bJsonPathArrayOfPointersSetStringArray);
    RUN_TEST(test_bJsonPathDel);
    RUN_TEST(test_bJsonPathDetach);
    RUN_TEST(test_bJsonPathDuplicate);
    RUN_TEST(test_bJsonPathIsExist);

    UNITY_END();
    return(0);
}