/*******************************************************************************
* ZHT Communication & technology Co.Ltd 2019-2029 Copyright reserved
* File : bJsonPathTestApi.h
* Function : bJsonPath test;
* Author : wuliuzhi
* Created on: 2025.07.14
*
* Modification history:
* Ver Who Date Changes
* ------------------------------------------------------------------------------
* V1.0 wanghy 2025/07/14 First release
********************************************************************************/
#include "bJsonPathTestApi.h"

const char *sFileCtxt = 
    "{"
        "\"University\": [{"
                "\"library\": {"
                    "\"books\": {"
                        "\"name\": \"c language\""
                    "}"
                "}"
            "},{"
                "\"library\": {"
                    "\"books\": {"
                        "\"name\": \"c++ language\","
                        "\"appraise\" : [\"good\", \"very good\", \"wonderful\"],"
                        "\"price\" : [111, 120.01, 114.12345678901234567890]"
                    "}"
                "}"
            "}"
        "]"
    "}";


void test_bJsonPathGet(void)
{
    cJSON *root = cJSON_Parse(sFileCtxt);

    cJSON *item = bJsonPathGet(root, "University.0.library.books.name");
    TEST_ASSERT_NOT_NULL(item);
    TEST_ASSERT_EQUAL_STRING("c language", cJSON_GetStringValue(item));

    item = bJsonPathGet(root, "University");
    TEST_ASSERT_NOT_NULL(item);
    TEST_ASSERT_EQUAL_PTR(item, cJSON_GetObjectItem(root, "University"));

    item = bJsonPathGet(NULL, "University.0.library.books.name");
    TEST_ASSERT_NULL(item);

    item = bJsonPathGet(root, NULL);
    TEST_ASSERT_NULL(item);

    item = bJsonPathGet(root, "");
    TEST_ASSERT_NULL(item);

    item = bJsonPathGet(root, "University.0.library.books.noexist");
    TEST_ASSERT_NULL(item);

    cJSON_Delete(root);
}

void test_bJsonPathGetArraySize(void)
{
    cJSON *root = cJSON_Parse(sFileCtxt);
    int rc = bJsonPathGetArraySize(root, "University.1.library.books.appraise");
    TEST_ASSERT_EQUAL_INT(rc, 3);

    rc = bJsonPathGetArraySize(root, "University.1.library.books.name");
    TEST_ASSERT_EQUAL_INT(rc, 0);

    rc = bJsonPathGetArraySize(root, "University.1.library.books.name.noexist");
    TEST_ASSERT_EQUAL_INT(rc, 0);

    cJSON_Delete(root);
}

void test_bJsonPathGetDouble(void)
{
    double value = 0.0;
    cJSON *root = cJSON_Parse(sFileCtxt);
    int rc = bJsonPathGetDouble(root, "University.1.library.books.price.2", &value);
    TEST_ASSERT_EQUAL_INT(rc, 0);
    TEST_ASSERT_EQUAL_DOUBLE(value, 114.12345678901234567890);

    rc = bJsonPathGetDouble(root, "University.1.library.books.price.3", &value);
    TEST_ASSERT_EQUAL_INT(rc, -1);

    rc = bJsonPathGetDouble(root, "University.1.library.books", &value);
    TEST_ASSERT_EQUAL_INT(rc, -1);

    cJSON_Delete(root);
}

void test_bJsonPathGetFloat(void)
{
    float value = 0.0;
    cJSON *root = cJSON_Parse(sFileCtxt);
    int rc = bJsonPathGetFloat(root, "University.1.library.books.price.2", &value);
    TEST_ASSERT_EQUAL_INT(rc, 0);
    TEST_ASSERT_EQUAL_FLOAT(value, 114.12345678901234567890);

    rc = bJsonPathGetFloat(root, "University.1.library.books.price.3", &value);
    TEST_ASSERT_EQUAL_INT(rc, -1);

    rc = bJsonPathGetFloat(root, "University.1.library.books", &value);
    TEST_ASSERT_EQUAL_INT(rc, -1);

    cJSON_Delete(root);
}

void test_bJsonPathGetInteger(void)
{
    int value = 0;
    cJSON *root = cJSON_Parse(sFileCtxt);
    int rc = bJsonPathGetInteger(root, "University.1.library.books.price.0", &value);
    TEST_ASSERT_EQUAL_INT(rc, 0);
    TEST_ASSERT_EQUAL_INT(value, 111);

    rc = bJsonPathGetInteger(root, "University.1.library.books.price.2", &value);
    TEST_ASSERT_EQUAL_INT(rc, 0);
    TEST_ASSERT_EQUAL_INT(value, 114);

    rc = bJsonPathGetInteger(root, "University.1.library.books.price.3", &value);
    TEST_ASSERT_EQUAL_INT(rc, -1);

    rc = bJsonPathGetInteger(root, "University.1.library.books", &value);
    TEST_ASSERT_EQUAL_INT(rc, -1);

    cJSON_Delete(root);
}

void test_bJsonPathGetString(void)
{
    char buffer[128];
    cJSON *root = cJSON_Parse(sFileCtxt);
    int rc = bJsonPathGetString(root, "University.0.library.books.name", buffer, sizeof(buffer));
    TEST_ASSERT_EQUAL_INT(rc, 0);
    TEST_ASSERT_EQUAL_STRING("c language", buffer);

    rc = bJsonPathGetString(root, "University.0.library.books.noexist", buffer, sizeof(buffer));
    TEST_ASSERT_EQUAL_INT(rc, -1);

    rc = bJsonPathGetString(root, "University.0.library.books", buffer, sizeof(buffer));
    TEST_ASSERT_EQUAL_INT(rc, -1);

    cJSON_Delete(root);
}

void test_bJsonPathGetDoubleArray(void)
{
    double value[10];
    double rValue[10] = {111, 120.01, 114.12345678901234567890};
    cJSON *root = cJSON_Parse(sFileCtxt);
    int rc = bJsonPathGetDoubleArray(root, "University.1.library.books.price", value, 0);
    TEST_ASSERT_EQUAL_INT(rc, 0);

    rc = bJsonPathGetDoubleArray(root, "University.1.library.books.price", value, 1);
    TEST_ASSERT_EQUAL_INT(rc, 1);
    TEST_ASSERT_EQUAL_DOUBLE_ARRAY(value, rValue, 1);

    rc = bJsonPathGetDoubleArray(root, "University.1.library.books.price", value, 2);
    TEST_ASSERT_EQUAL_INT(rc, 2);
    TEST_ASSERT_EQUAL_DOUBLE_ARRAY(value, rValue, 2);

    rc = bJsonPathGetDoubleArray(root, "University.1.library.books.price", value, 3);
    TEST_ASSERT_EQUAL_INT(rc, 3);
    TEST_ASSERT_EQUAL_DOUBLE_ARRAY(value, rValue, 3);

    rc = bJsonPathGetDoubleArray(root, "University.1.library.books.price", value, 4);
    TEST_ASSERT_EQUAL_INT(rc, 3);
    TEST_ASSERT_EQUAL_DOUBLE_ARRAY(value, rValue, 3);

    rc = bJsonPathGetDoubleArray(root, "University.1.library.books.noExist", value, sizeof(value)/sizeof(value[0]));
    TEST_ASSERT_EQUAL_INT(rc, 0);

    cJSON_Delete(root);
}

void test_bJsonPathGetFloatArray(void)
{
    float value[10];
    float rValue[10] = {111, 120.01, 114.12345678901234567890};
    cJSON *root = cJSON_Parse(sFileCtxt);
    int rc = bJsonPathGetFloatArray(root, "University.1.library.books.price", value, 0);
    TEST_ASSERT_EQUAL_INT(rc, 0);

    rc = bJsonPathGetFloatArray(root, "University.1.library.books.price", value, 1);
    TEST_ASSERT_EQUAL_INT(rc, 1);
    TEST_ASSERT_EQUAL_FLOAT_ARRAY(value, rValue, 1);

    rc = bJsonPathGetFloatArray(root, "University.1.library.books.price", value, 2);
    TEST_ASSERT_EQUAL_INT(rc, 2);
    TEST_ASSERT_EQUAL_FLOAT_ARRAY(value, rValue, 2);

    rc = bJsonPathGetFloatArray(root, "University.1.library.books.price", value, 3);
    TEST_ASSERT_EQUAL_INT(rc, 3);
    TEST_ASSERT_EQUAL_FLOAT_ARRAY(value, rValue, 3);

    rc = bJsonPathGetFloatArray(root, "University.1.library.books.price", value, 4);
    TEST_ASSERT_EQUAL_INT(rc, 3);
    TEST_ASSERT_EQUAL_FLOAT_ARRAY(value, rValue, 3);

    rc = bJsonPathGetFloatArray(root, "University.1.library.books.noExist", value, sizeof(value)/sizeof(value[0]));
    TEST_ASSERT_EQUAL_INT(rc, 0);

    cJSON_Delete(root);
}

void test_bJsonPathGetIntegerArray(void)
{
    int value[10];
    int rValue[10] = {111, 120, 114};
    cJSON *root = cJSON_Parse(sFileCtxt);
    int rc = bJsonPathGetIntegerArray(root, "University.1.library.books.price", value, 0);
    TEST_ASSERT_EQUAL_INT(rc, 0);

    rc = bJsonPathGetIntegerArray(root, "University.1.library.books.price", value, 1);
    TEST_ASSERT_EQUAL_INT(rc, 1);
    TEST_ASSERT_EQUAL_INT_ARRAY(value, rValue, 1);

    rc = bJsonPathGetIntegerArray(root, "University.1.library.books.price", value, 2);
    TEST_ASSERT_EQUAL_INT(rc, 2);
    TEST_ASSERT_EQUAL_INT_ARRAY(value, rValue, 2);

    rc = bJsonPathGetIntegerArray(root, "University.1.library.books.price", value, 3);
    TEST_ASSERT_EQUAL_INT(rc, 3);
    TEST_ASSERT_EQUAL_INT_ARRAY(value, rValue, 3);

    rc = bJsonPathGetIntegerArray(root, "University.1.library.books.price", value, 4);
    TEST_ASSERT_EQUAL_INT(rc, 3);
    TEST_ASSERT_EQUAL_INT_ARRAY(value, rValue, 3);

    rc = bJsonPathGetIntegerArray(root, "University.1.library.books.noExist", value, sizeof(value)/sizeof(value[0]));
    TEST_ASSERT_EQUAL_INT(rc, 0);

    cJSON_Delete(root);
}

void test_bJsonPath2dArrayGetStringArray(void)
{
    char value[5][16];
    char rValue[5][16] = { "good", "very good", "wonderful"};
    cJSON *root = cJSON_Parse(sFileCtxt);
    int rc = bJsonPath2dArrayGetStringArray(root, "University.1.library.books.appraise", (char*)value, 0, 16);
    TEST_ASSERT_EQUAL_INT(rc, 0);

    rc = bJsonPath2dArrayGetStringArray(root, "University.1.library.books.appraise", (char*)value, 1, 16);
    TEST_ASSERT_EQUAL_INT(rc, 1);
    TEST_ASSERT_EQUAL_MEMORY_ARRAY(value, rValue, sizeof(value)/sizeof(value[0]), 1);

    rc = bJsonPath2dArrayGetStringArray(root, "University.1.library.books.appraise", (char*)value, 2, 16);
    TEST_ASSERT_EQUAL_INT(rc, 2);
    TEST_ASSERT_EQUAL_MEMORY_ARRAY(value, rValue, sizeof(value)/sizeof(value[0]), 2);

    rc = bJsonPath2dArrayGetStringArray(root, "University.1.library.books.appraise", (char*)value, 3, 16);
    TEST_ASSERT_EQUAL_INT(rc, 3);
    TEST_ASSERT_EQUAL_MEMORY_ARRAY(value, rValue, sizeof(value)/sizeof(value[0]), 3);

    rc = bJsonPath2dArrayGetStringArray(root, "University.1.library.books.appraise", (char*)value, 4, 16);
    TEST_ASSERT_EQUAL_INT(rc, 3);
    TEST_ASSERT_EQUAL_MEMORY_ARRAY(value, rValue, sizeof(value)/sizeof(value[0]), 3);

    rc = bJsonPath2dArrayGetStringArray(root, "University.1.library.books.noExist", (char*)value, 5, 16);
    TEST_ASSERT_EQUAL_INT(rc, 0);

    cJSON_Delete(root);
}

void test_bJsonPathArrayOfPointersGetStringArray(void)
{
    int i = 0;
    char *value[5];
    char *rValue[5] = { "good", "very good", "wonderful"};
    for(i=0; i<sizeof(value)/sizeof(value[0]); i++){
        value[i] = malloc(16);
    }

    cJSON *root = cJSON_Parse(sFileCtxt);
    int rc = bJsonPathArrayOfPointersGetStringArray(root, "University.1.library.books.appraise", value, 0, 16);
    TEST_ASSERT_EQUAL_INT(rc, 0);

    rc = bJsonPathArrayOfPointersGetStringArray(root, "University.1.library.books.appraise", value, 1, 16);
    TEST_ASSERT_EQUAL_INT(rc, 1);
    TEST_ASSERT_EQUAL_STRING_ARRAY(value, rValue, 1);

    rc = bJsonPathArrayOfPointersGetStringArray(root, "University.1.library.books.appraise", value, 2, 16);
    TEST_ASSERT_EQUAL_INT(rc, 2);
    TEST_ASSERT_EQUAL_STRING_ARRAY(value, rValue, 2);

    rc = bJsonPathArrayOfPointersGetStringArray(root, "University.1.library.books.appraise", value, 3, 16);
    TEST_ASSERT_EQUAL_INT(rc, 3);
    TEST_ASSERT_EQUAL_STRING_ARRAY(value, rValue, 3);

    rc = bJsonPathArrayOfPointersGetStringArray(root, "University.1.library.books.appraise", value, 4, 16);
    TEST_ASSERT_EQUAL_INT(rc, 3);
    TEST_ASSERT_EQUAL_STRING_ARRAY(value, rValue, 3);

    rc = bJsonPathArrayOfPointersGetStringArray(root, "University.1.library.books.noExist", value, 5, 16);
    TEST_ASSERT_EQUAL_INT(rc, 0);

    for(i=0; i<sizeof(value)/sizeof(value[0]); i++){
        free(value[i]);
    }
    cJSON_Delete(root);
}

void test_bJsonPathUpdate(void)
{
    cJSON *root = NULL;
    int rc = bJsonPathUpdate(root, "University", cJSON_CreateString("shenZhen University"));
    TEST_ASSERT_EQUAL_INT(rc, -1);

    root = cJSON_CreateObject();
    rc = bJsonPathUpdate(root, "University", cJSON_CreateString("shenZhen University"));
    TEST_ASSERT_EQUAL_INT(rc, -1);

    cJSON_AddStringToObject(root, "University", "shenZhen University");
    rc = bJsonPathUpdate(root, "University", cJSON_CreateString("hunan University"));
    TEST_ASSERT_EQUAL_INT(rc, 0);
    TEST_ASSERT_EQUAL_STRING("hunan University", cJSON_GetStringValue(bJsonPathGet(root, "University")));

    cJSON *universityArr = cJSON_AddArrayToObject(root, "cities");
    rc = bJsonPathUpdate(root, "cities.0", cJSON_CreateString("shenZhen"));
    TEST_ASSERT_EQUAL_INT(rc, -1);

    cJSON_AddItemToArray(universityArr, cJSON_CreateObject());
    cJSON *university0New = cJSON_Parse(sFileCtxt);
    char *university0NewStr = cJSON_Print(university0New);
    rc = bJsonPathUpdate(root, "cities.0", university0New);
    TEST_ASSERT_EQUAL_INT(rc, 0);
    cJSON *university0 = cJSON_GetArrayItem(universityArr, 0);
    char *university0Str = cJSON_Print(university0);
    TEST_ASSERT_EQUAL_STRING(university0NewStr, university0Str);
    cJSON_free(university0NewStr);
    cJSON_free(university0Str);
 
    cJSON_Delete(root);
}

void test_bJsonPathUpdateDouble(void)
{
    double value = 999.123456789;
    double rValue = 0.0;
    cJSON *root = cJSON_Parse(sFileCtxt);

    int rc = bJsonPathUpdateDouble(root, "University", value);
    TEST_ASSERT_EQUAL_INT(rc, -1);

    rc = bJsonPathUpdateDouble(root, "University.0", value);
    TEST_ASSERT_EQUAL_INT(rc, -1);

    rc = bJsonPathUpdateDouble(root, "University.1.library.books.name", value);
    TEST_ASSERT_EQUAL_INT(rc, -1);

    rc = bJsonPathUpdateDouble(root, "University.1.library.books.price.1", value);
    TEST_ASSERT_EQUAL_INT(rc, 0);
    bJsonPathGetDouble(root, "University.1.library.books.price.1", &rValue);
    TEST_ASSERT_EQUAL_DOUBLE(value, rValue);

    cJSON_Delete(root);
}

void test_bJsonPathUpdateFloat(void)
{
    float value = 999.123456789;
    float rValue = 0.0;
    cJSON *root = cJSON_Parse(sFileCtxt);

    int rc = bJsonPathUpdateFloat(root, "University", value);
    TEST_ASSERT_EQUAL_INT(rc, -1);

    rc = bJsonPathUpdateFloat(root, "University.0", value);
    TEST_ASSERT_EQUAL_INT(rc, -1);

    rc = bJsonPathUpdateFloat(root, "University.1.library.books.name", value);
    TEST_ASSERT_EQUAL_INT(rc, -1);

    rc = bJsonPathUpdateFloat(root, "University.1.library.books.price.1", value);
    TEST_ASSERT_EQUAL_INT(rc, 0);
    bJsonPathGetFloat(root, "University.1.library.books.price.1", &rValue);
    TEST_ASSERT_EQUAL_DOUBLE(value, rValue);

    cJSON_Delete(root);
}

void test_bJsonPathUpdateInteger(void)
{
    int value = 999;
    int rValue = 0;
    cJSON *root = cJSON_Parse(sFileCtxt);

    int rc = bJsonPathUpdateInteger(root, "University", value);
    TEST_ASSERT_EQUAL_INT(rc, -1);

    rc = bJsonPathUpdateInteger(root, "University.0", value);
    TEST_ASSERT_EQUAL_INT(rc, -1);

    rc = bJsonPathUpdateInteger(root, "University.1.library.books.name", value);
    TEST_ASSERT_EQUAL_INT(rc, -1);

    rc = bJsonPathUpdateInteger(root, "University.1.library.books.price.1", value);
    TEST_ASSERT_EQUAL_INT(rc, 0);
    bJsonPathGetInteger(root, "University.1.library.books.price.1", &rValue);
    TEST_ASSERT_EQUAL_INT(value, rValue);

    cJSON_Delete(root);
}

void test_bJsonPathUpdateString(void)
{
    char value[] = "fantastic";
    char rValue[16];
    cJSON *root = cJSON_Parse(sFileCtxt);

    int rc = bJsonPathUpdateString(root, "University", value);
    TEST_ASSERT_EQUAL_INT(rc, -1);

    rc = bJsonPathUpdateString(root, "University.0", value);
    TEST_ASSERT_EQUAL_INT(rc, -1);

    rc = bJsonPathUpdateString(root, "University.1.library.books", value);
    TEST_ASSERT_EQUAL_INT(rc, -1);

    rc = bJsonPathUpdateString(root, "University.1.library.books.appraise.3", value);
    TEST_ASSERT_EQUAL_INT(rc, -1);

    rc = bJsonPathUpdateString(root, "University.1.library.books.appraise.2", value);
    TEST_ASSERT_EQUAL_INT(rc, 0);
    bJsonPathGetString(root, "University.1.library.books.appraise.2", rValue, sizeof(rValue));
    TEST_ASSERT_EQUAL_STRING(value, rValue);

    cJSON_Delete(root);
}

void test_bJsonPathSet(void)
{
    char buffer[16];
    cJSON *root = cJSON_Parse(sFileCtxt);

    int rc = bJsonPathSet(&root, "University.library.1.books.appraise", cJSON_CreateString("fantastic"));
    TEST_ASSERT_EQUAL_INT(rc, 0);
    bJsonPathGetString(root, "University.library.1.books.appraise", buffer, sizeof(buffer));
    TEST_ASSERT_EQUAL_STRING("fantastic", buffer);

    rc = bJsonPathSet(&root, "University.library.1.books", cJSON_CreateString("python handbook"));
    TEST_ASSERT_EQUAL_INT(rc, 0);
    bJsonPathGetString(root, "University.library.1.books", buffer, sizeof(buffer));
    TEST_ASSERT_EQUAL_STRING("python handbook", buffer);

    rc = bJsonPathSet(&root, "University.location", cJSON_CreateString("shen Zhen"));
    TEST_ASSERT_EQUAL_INT(rc, 0);
    bJsonPathGetString(root, "University.location", buffer, sizeof(buffer));
    TEST_ASSERT_EQUAL_STRING("shen Zhen", buffer);

    rc = bJsonPathSet(&root, "University.Committee.member.1.name", cJSON_CreateString("wagnhy"));
    TEST_ASSERT_EQUAL_INT(rc, 0);
    bJsonPathGetString(root, "University.Committee.member.1.name", buffer, sizeof(buffer));
    TEST_ASSERT_EQUAL_STRING("wagnhy", buffer);

    rc = bJsonPathSet(&root, "University.Committee.member.1.age", cJSON_CreateNumber(31));
    TEST_ASSERT_EQUAL_INT(rc, 0);
    int age = 0;
    bJsonPathGetInteger(root, "University.Committee.member.1.age", &age);
    TEST_ASSERT_EQUAL_INT(31, age);

    cJSON_Delete(root);
}

void test_bJsonPathSetDouble(void)
{
    double value = 0;
    cJSON *root = cJSON_Parse(sFileCtxt);

    int rc = bJsonPathSetDouble(&root, "University.library.1.books.price.2", 100.100);
    TEST_ASSERT_EQUAL_INT(rc, 0);
    bJsonPathGetDouble(root, "University.library.1.books.price.2", &value);
    TEST_ASSERT_EQUAL_DOUBLE(value, 100.100);

    rc = bJsonPathSetDouble(&root, "University.library.1.books.appraise", 101.01);
    TEST_ASSERT_EQUAL_INT(rc, 0);
    bJsonPathGetDouble(root, "University.library.1.books.appraise", &value);
    TEST_ASSERT_EQUAL_DOUBLE(value, 101.01);

    rc = bJsonPathSetDouble(&root, "University.library.1", 102.102);
    TEST_ASSERT_EQUAL_INT(rc, 0);
    bJsonPathGetDouble(root, "University.library.1", &value);
    TEST_ASSERT_EQUAL_DOUBLE(value, 102.102);

    rc = bJsonPathSetDouble(&root, "University.Committee.member.1.age", 31.667);
    TEST_ASSERT_EQUAL_INT(rc, 0);
    bJsonPathGetDouble(root, "University.Committee.member.1.age", &value);
    TEST_ASSERT_EQUAL_DOUBLE(value, 31.667);

    cJSON_Delete(root);
}

void test_bJsonPathSetFloat(void)
{
    float value = 0;
    cJSON *root = cJSON_Parse(sFileCtxt);

    int rc = bJsonPathSetFloat(&root, "University.library.1.books.price.2", 100.100);
    TEST_ASSERT_EQUAL_INT(rc, 0);
    bJsonPathGetFloat(root, "University.library.1.books.price.2", &value);
    TEST_ASSERT_EQUAL_FLOAT(value, 100.100);

    rc = bJsonPathSetFloat(&root, "University.library.1.books.appraise", 101.01);
    TEST_ASSERT_EQUAL_INT(rc, 0);
    bJsonPathGetFloat(root, "University.library.1.books.appraise", &value);
    TEST_ASSERT_EQUAL_FLOAT(value, 101.01);

    rc = bJsonPathSetFloat(&root, "University.library.1", 102.102);
    TEST_ASSERT_EQUAL_INT(rc, 0);
    bJsonPathGetFloat(root, "University.library.1", &value);
    TEST_ASSERT_EQUAL_FLOAT(value, 102.102);

    rc = bJsonPathSetFloat(&root, "University.Committee.member.1.age", 31.667);
    TEST_ASSERT_EQUAL_INT(rc, 0);
    bJsonPathGetFloat(root, "University.Committee.member.1.age", &value);
    TEST_ASSERT_EQUAL_FLOAT(value, 31.667);

    cJSON_Delete(root);
}

void test_bJsonPathSetInteger(void)
{
    int value = 0;
    cJSON *root = cJSON_Parse(sFileCtxt);

    int rc = bJsonPathSetInteger(&root, "University.library.1.books.price.2", 100);
    TEST_ASSERT_EQUAL_INT(rc, 0);
    bJsonPathGetInteger(root, "University.library.1.books.price.2", &value);
    TEST_ASSERT_EQUAL_INT(value, 100);

    rc = bJsonPathSetInteger(&root, "University.library.1.books.appraise", 101);
    TEST_ASSERT_EQUAL_INT(rc, 0);
    bJsonPathGetInteger(root, "University.library.1.books.appraise", &value);
    TEST_ASSERT_EQUAL_INT(value, 101);

    rc = bJsonPathSetInteger(&root, "University.library.1", 102);
    TEST_ASSERT_EQUAL_INT(rc, 0);
    bJsonPathGetInteger(root, "University.library.1", &value);
    TEST_ASSERT_EQUAL_INT(value, 102);

    rc = bJsonPathSetInteger(&root, "University.Committee.member.1.age", 31);
    TEST_ASSERT_EQUAL_INT(rc, 0);
    bJsonPathGetInteger(root, "University.Committee.member.1.age", &value);
    TEST_ASSERT_EQUAL_INT(value, 31);

    cJSON_Delete(root);
}

void test_bJsonPathSetString(void)
{
    char value[20];
    cJSON *root = cJSON_Parse(sFileCtxt);

    int rc = bJsonPathSetString(&root, "University.library.1.books.price.2", "$114.123");
    TEST_ASSERT_EQUAL_INT(rc, 0);
    bJsonPathGetString(root, "University.library.1.books.price.2", value, sizeof(value));
    TEST_ASSERT_EQUAL_STRING(value, "$114.123");

    rc = bJsonPathSetString(&root, "University.library.1.books.price.2", "perishing");
    TEST_ASSERT_EQUAL_INT(rc, 0);
    bJsonPathGetString(root, "University.library.1.books.price.2", value, sizeof(value));
    TEST_ASSERT_EQUAL_STRING(value, "perishing");

    rc = bJsonPathSetString(&root, "University.library", "Shen Zhen library");
    TEST_ASSERT_EQUAL_INT(rc, 0);
    bJsonPathGetString(root, "University.library", value, sizeof(value));
    TEST_ASSERT_EQUAL_STRING(value, "Shen Zhen library");

    rc = bJsonPathSetString(&root, "cities.0.location", "logi:22.3435");
    TEST_ASSERT_EQUAL_INT(rc, 0);
    bJsonPathGetString(root, "cities.0.location", value, sizeof(value));
    TEST_ASSERT_EQUAL_STRING(value, "logi:22.3435");

    cJSON_Delete(root);
}

void test_bJsonPathSetDoubleArray(void)
{
    double value[5] = {10.123, 20.123, 30.123, 40.123, 50.123};
    double rValue[5];
    cJSON *root = cJSON_Parse(sFileCtxt);

    int rc = bJsonPathSetDoubleArray(&root, "University.library.books.price.2", value, sizeof(value)/sizeof(value[0]));
    TEST_ASSERT_EQUAL_INT(rc, 0);
    bJsonPathGetDoubleArray(root, "University.library.books.price.2", rValue, sizeof(rValue)/sizeof(rValue[0]));
    TEST_ASSERT_EQUAL_DOUBLE_ARRAY(value, rValue, sizeof(value)/sizeof(value[0]));

    rc = bJsonPathSetDoubleArray(&root, "store.apple.price", value, sizeof(value)/sizeof(value[0]));
    TEST_ASSERT_EQUAL_INT(rc, 0);
    bJsonPathGetDoubleArray(root, "store.apple.price", rValue, sizeof(rValue)/sizeof(rValue[0]));
    TEST_ASSERT_EQUAL_DOUBLE_ARRAY(value, rValue, sizeof(value)/sizeof(value[0]));

    cJSON_Delete(root);
}

void test_bJsonPathSetFloatArray(void)
{
    float value[5] = {10.123, 20.123, 30.123, 40.123, 50.123};
    float rValue[5];
    cJSON *root = cJSON_Parse(sFileCtxt);

    int rc = bJsonPathSetFloatArray(&root, "University.library.books.price.2", value, sizeof(value)/sizeof(value[0]));
    TEST_ASSERT_EQUAL_INT(rc, 0);
    bJsonPathGetFloatArray(root, "University.library.books.price.2", rValue, sizeof(rValue)/sizeof(rValue[0]));
    TEST_ASSERT_EQUAL_FLOAT_ARRAY(value, rValue, sizeof(value)/sizeof(value[0]));

    rc = bJsonPathSetFloatArray(&root, "store.apple.price", value, sizeof(value)/sizeof(value[0]));
    TEST_ASSERT_EQUAL_INT(rc, 0);
    bJsonPathGetFloatArray(root, "store.apple.price", rValue, sizeof(rValue)/sizeof(rValue[0]));
    TEST_ASSERT_EQUAL_FLOAT_ARRAY(value, rValue, sizeof(value)/sizeof(value[0]));

    cJSON_Delete(root);
}

void test_bJsonPathSetIntegerArray(void)
{
    int value[5] = {10, 20, 30, 40, 50};
    int rValue[5];
    cJSON *root = cJSON_Parse(sFileCtxt);

    int rc = bJsonPathSetIntegerArray(&root, "University.library.books.price.2", value, sizeof(value)/sizeof(value[0]));
    TEST_ASSERT_EQUAL_INT(rc, 0);
    bJsonPathGetIntegerArray(root, "University.library.books.price.2", rValue, sizeof(rValue)/sizeof(rValue[0]));
    TEST_ASSERT_EQUAL_INT_ARRAY(value, rValue, sizeof(value)/sizeof(value[0]));

    rc = bJsonPathSetIntegerArray(&root, "store.apple.price", value, sizeof(value)/sizeof(value[0]));
    TEST_ASSERT_EQUAL_INT(rc, 0);
    bJsonPathGetIntegerArray(root, "store.apple.price", rValue, sizeof(rValue)/sizeof(rValue[0]));
    TEST_ASSERT_EQUAL_INT_ARRAY(value, rValue, sizeof(value)/sizeof(value[0]));

    cJSON_Delete(root);
}

void test_bJsonPath2dArraySetStringArray(void)
{
    char arrString[3][20] = { "good", "wonderful", "awfu"};
    char rValue[3][20];
    cJSON *root = cJSON_Parse(sFileCtxt);

    int rc = bJsonPath2dArraySetStringArray(&root, "University.library.books.appraise", (char*)arrString, 3, 20);
    TEST_ASSERT_EQUAL_INT(rc, 3);
    bJsonPath2dArrayGetStringArray(root, "University.library.books.appraise", (char*)rValue, 3, 20);
    TEST_ASSERT_EQUAL_MEMORY(arrString, rValue, sizeof(rValue));

    rc = bJsonPath2dArraySetStringArray(&root, "store.apple.appraise", (char*)arrString, 3, 20);
    TEST_ASSERT_EQUAL_INT(rc, 3);
    bJsonPath2dArrayGetStringArray(root, "store.apple.appraise", (char*)rValue, 3, 20);
    TEST_ASSERT_EQUAL_MEMORY(arrString, rValue, sizeof(rValue));

    rc = bJsonPath2dArraySetStringArray(&root, "store.tomato.appraise", (char*)arrString, 3, 1);
    TEST_ASSERT_EQUAL_INT(rc, 0);

    cJSON_Delete(root);
}

void test_bJsonPathArrayOfPointersSetStringArray(void)
{
    char buf1[20], buf2[20], buf3[20];
    char *arrString[3] = { "good", "wonderful", "awfu"};
    char *rValue[3] = { buf1, buf2, buf3};
    cJSON *root = cJSON_Parse(sFileCtxt);

    int rc = bJsonPathArrayOfPointersSetStringArray(&root, "University.1.library.books.appraise", arrString, 3, 20);
    TEST_ASSERT_EQUAL_INT(rc, 3);
    bJsonPathArrayOfPointersGetStringArray(root, "University.1.library.books.appraise", rValue, 3, 20);
    TEST_ASSERT_EQUAL_STRING_ARRAY(arrString, rValue, 3);

    rc = bJsonPathArrayOfPointersSetStringArray(&root, "store.apple.appraise", arrString, 3, 20);
    TEST_ASSERT_EQUAL_INT(rc, 3);
    bJsonPathArrayOfPointersGetStringArray(root, "store.apple.appraise", rValue, 3, 20);
    TEST_ASSERT_EQUAL_STRING_ARRAY(arrString, rValue, 3);

    rc = bJsonPathArrayOfPointersSetStringArray(&root, "store.tomato.appraise", arrString, 3, 1);
    TEST_ASSERT_EQUAL_INT(rc, 0);

    cJSON_Delete(root);
}

void test_bJsonPathDel(void)
{
    cJSON *root = cJSON_Parse(sFileCtxt);

    int rc = bJsonPathDel(root, "University.1.library");
    TEST_ASSERT_EQUAL_INT(rc, 0);
    cJSON *item = bJsonPathGet(root, "University.1.library");
    TEST_ASSERT_NULL(item);

    rc = bJsonPathDel(root, "University");
    TEST_ASSERT_EQUAL_INT(rc, 0);
    item = bJsonPathGet(root, "University");
    TEST_ASSERT_NULL(item);

    rc = bJsonPathDel(root, "noExist");
    TEST_ASSERT_EQUAL_INT(rc, 1);

    rc = bJsonPathDel(root, "noExist.noExixt");
    TEST_ASSERT_EQUAL_INT(rc, 1);

    cJSON_Delete(root);
}

void test_bJsonPathDetach(void)
{
    cJSON *root = cJSON_Parse(sFileCtxt);

    cJSON *item = bJsonPathGet(root, "University.1.library");
    char *itemStr = cJSON_Print(item);
    cJSON *rc = bJsonPathDetach(root, "University.1.library");
    char *rcStr = cJSON_Print(rc);
    TEST_ASSERT_EQUAL_STRING(itemStr, rcStr);
    cJSON_free(itemStr);
    cJSON_free(rcStr);
    cJSON_Delete(rc);

    item = bJsonPathGet(root, "University.noExist");
    TEST_ASSERT_NULL(item);

    item = bJsonPathGet(root, "University");
    itemStr = cJSON_Print(item);
    rc = bJsonPathDetach(root, "University");
    rcStr = cJSON_Print(rc);
    TEST_ASSERT_EQUAL_STRING(itemStr, rcStr);
    cJSON_free(itemStr);
    cJSON_free(rcStr);
    cJSON_Delete(rc);

    item = bJsonPathGet(root, "noExist.noExist");
    TEST_ASSERT_NULL(item);

    item = bJsonPathGet(root, "noExist");
    TEST_ASSERT_NULL(item);

    cJSON_Delete(root);
}

void test_bJsonPathDuplicate(void)
{
    cJSON *root = cJSON_Parse(sFileCtxt);

    cJSON *item = bJsonPathDuplicate(root, NULL, true);
    TEST_ASSERT_NOT_NULL(item);
    char *itemStr = cJSON_Print(item);
    char *rcStr = cJSON_Print(root);
    TEST_ASSERT_EQUAL_STRING(itemStr, rcStr);
    cJSON_free(itemStr);
    cJSON_free(rcStr);
    cJSON_Delete(item);

    item = bJsonPathDuplicate(root, "University.1.library", true);
    TEST_ASSERT_NOT_NULL(item);
    itemStr = cJSON_Print(item);
    cJSON *rc = bJsonPathGet(root, "University.1.library");
    rcStr = cJSON_Print(rc);
    TEST_ASSERT_EQUAL_STRING(itemStr, rcStr);
    cJSON_free(itemStr);
    cJSON_free(rcStr);
    cJSON_Delete(item);

    item = bJsonPathDuplicate(root, "University.1.library.books.name", false);
    TEST_ASSERT_NOT_NULL(item);
    itemStr = cJSON_Print(item);
    rc = bJsonPathGet(root, "University.1.library.books.name");
    rcStr = cJSON_Print(rc);
    TEST_ASSERT_EQUAL_STRING(itemStr, rcStr);
    cJSON_free(itemStr);
    cJSON_free(rcStr);
    cJSON_Delete(item);

    item = bJsonPathDuplicate(root, "University.1.library.books.noexist", true);
    TEST_ASSERT_NULL(item);

    item = bJsonPathDuplicate(root, "noexist.noexist", true);
    TEST_ASSERT_NULL(item);

    item = bJsonPathDuplicate(root, "noexist", true);
    TEST_ASSERT_NULL(item);

    cJSON_Delete(root);
}

void test_bJsonPathIsExist(void)
{
    cJSON *root = cJSON_Parse(sFileCtxt);

    int rc = bJsonPathIsExist(root, "University.1.library.books.price.2");
    TEST_ASSERT_EQUAL_INT(rc, 1);

    rc = bJsonPathIsExist(root, "University.1.library.books.price.3");
    TEST_ASSERT_EQUAL_INT(rc, 0);

    rc = bJsonPathIsExist(root, "University.1.library");
    TEST_ASSERT_EQUAL_INT(rc, 1);

    rc = bJsonPathIsExist(root, "University");
    TEST_ASSERT_EQUAL_INT(rc, 1);

    rc = bJsonPathIsExist(root, "noExist.noExist");
    TEST_ASSERT_EQUAL_INT(rc, 0);

    rc = bJsonPathIsExist(root, "noExist");
    TEST_ASSERT_EQUAL_INT(rc, 0);

    cJSON_Delete(root);
}