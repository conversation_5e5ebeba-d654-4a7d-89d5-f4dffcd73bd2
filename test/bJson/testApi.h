/*******************************************************************************
* ZHT Communication & technology Co.Ltd 2019-2029 Copyright reserved
* File : testApi.h
* Function : bJson test;
* Author : wuliuzhi
* Created on: 2025.07.02
*
* Modification history:
* Ver Who Date Changes
* ------------------------------------------------------------------------------
* V1.0 wuliuzhi 2025/07/02 First release
********************************************************************************/
#ifndef __TEST_API_H__
#define __TEST_API_H__

#include <unistd.h>
#include "unity.h"
#include "bJson.h"


void test_bJsonExportFile(void);
void test_bJsonImportFile(void);

void test_bJsonGetString(void);
void test_bJsonGetDouble(void);
void test_bJsonGetFloat(void);
void test_bJsonGetInteger(void);

void test_bJsonGetStringArray(void);
void test_bJsonGetDoubleArray(void);
void test_bJsonGetFloatArray(void);
void test_bJsonGetIntegerArray(void);

void test_bJsonUpdateString(void);
void test_bJsonUpdateDouble(void);
void test_bJsonUpdateFloat(void);
void test_bJsonUpdateInteger(void);

void test_bJsonUpdateStringArray(void);
void test_bJsonUpdateDoubleArray(void);
void test_bJsonUpdateFloatArray(void);
void test_bJsonUpdateIntegerArray(void);

#endif
