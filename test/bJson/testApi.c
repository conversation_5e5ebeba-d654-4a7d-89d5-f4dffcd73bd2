/*******************************************************************************
* ZHT Communication & technology Co.Ltd 2019-2029 Copyright reserved
* File : testApi.c
* Function : bJson test;
* Author : wuliuzhi
* Created on: 2025.07.02
*
* Modification history:
* Ver Who Date Changes
* ------------------------------------------------------------------------------
* V1.0 wuliuzhi 2025/07/02 First release
********************************************************************************/
#include "testApi.h"


void test_bJsonExportFile(void)
{
    int ret;
    char *fileName = "./image.json";
    cJSON *root = NULL;
    cJSON *img = NULL;
    cJSON *thm = NULL;
    int ids[4] = { 116, 943, 234, 38793 };

    root = cJSON_CreateObject();
    cJSON_AddItemToObject(root, "Image", img = cJSON_CreateObject());
    cJSON_AddNumberToObject(img, "Width", 800);
    cJSON_AddNumberToObject(img, "Height", 600);
    cJSON_AddStringToObject(img, "Title", "View from 15th Floor");
    cJSON_AddItemToObject(img, "Thumbnail", thm = cJSON_CreateObject());
    cJSON_AddStringToObject(thm, "Url", "http:/*www.example.com/image/481989943");
    cJSON_AddNumberToObject(thm, "Height", 125);
    cJSON_AddStringToObject(thm, "Width", "100");
    cJSON_AddItemToObject(img, "IDs", cJSON_CreateIntArray(ids, 4));

    ret = bJsonExportFile(root, fileName);
    TEST_ASSERT_EQUAL_INT(0, ret);

    cJSON_Delete(root);
}

void test_bJsonImportFile(void)
{
    char *fileName = "./image.json";
    cJSON *root = NULL;

    root = bJsonImportFile(fileName);
    TEST_ASSERT_NOT_NULL(root);

    cJSON_Delete(root);

    remove(fileName);
}

void test_bJsonGetString(void)
{
    int ret;
    char *kCity = "City"; char *vCity = "ShenZhen"; char rCity[64] = {0};

    /* create object */
    cJSON *tree = cJSON_CreateObject();
    cJSON_AddStringToObject(tree, kCity, vCity);

    /* read value */
    ret = bJsonGetString(tree, kCity, rCity, sizeof(rCity));
    TEST_ASSERT_EQUAL_INT(0, ret);

    cJSON_Delete(tree);

    TEST_ASSERT_EQUAL_STRING(vCity, rCity);
}

void test_bJsonGetDouble(void)
{
    int ret;
    char *kWidth = "Width"; double vWidth = 1920.0265176214; double rWidth = 0;

    /* create object */
    cJSON *tree = cJSON_CreateObject();
    cJSON_AddNumberToObject(tree, kWidth, vWidth);

    /* read value */
    ret = bJsonGetDouble(tree, kWidth, &rWidth);
    TEST_ASSERT_EQUAL_INT(0, ret);

    cJSON_Delete(tree);

    TEST_ASSERT_EQUAL_MEMORY(&vWidth, &rWidth, sizeof(double));
    TEST_ASSERT_EQUAL_DOUBLE(vWidth, rWidth);
}

void test_bJsonGetFloat(void)
{
    int ret;
    char *kWidth = "Width"; float vWidth = 1920.026; float rWidth = 0;

    /* create object */
    cJSON *tree = cJSON_CreateObject();
    cJSON_AddNumberToObject(tree, kWidth, vWidth);

    /* read value */
    ret = bJsonGetFloat(tree, kWidth, &rWidth);
    TEST_ASSERT_EQUAL_INT(0, ret);

    cJSON_Delete(tree);

    TEST_ASSERT_EQUAL_MEMORY(&vWidth, &rWidth, sizeof(float));
    TEST_ASSERT_EQUAL_FLOAT(vWidth, rWidth);
}

void test_bJsonGetInteger(void)
{
    int ret;
    char *kWidth = "Width"; int vWidth = 1920; int rWidth = 0;

    /* create object */
    cJSON *tree = cJSON_CreateObject();
    cJSON_AddNumberToObject(tree, kWidth, vWidth);

    /* read value */
    ret = bJsonGetInteger(tree, kWidth, &rWidth);
    TEST_ASSERT_EQUAL_INT(0, ret);

    cJSON_Delete(tree);

    TEST_ASSERT_EQUAL_MEMORY(&vWidth, &rWidth, sizeof(int));
    TEST_ASSERT_EQUAL_INT(vWidth, rWidth);
}

void test_bJsonGetStringArray(void)
{
    int ret;
    char *kWeek = "week";
    const char *vWeek[7] = {
        "Sunday",
        "Monday",
        "Tuesday",
        "Wednesday",
        "Thursday",
        "Friday",
        "Saturday"
    };

    /* create object */
    cJSON *tree = cJSON_CreateObject();
    cJSON *array = cJSON_CreateStringArray(vWeek, sizeof(vWeek) / sizeof(vWeek[0]));
    cJSON_AddItemToObject(tree, kWeek, array);

    char rWeek[7][16] = {0};
    char *bWeek[7] = {0};

    ret = bJsonStrPtrToArray(bWeek, (sizeof(bWeek) / sizeof(bWeek[0])),
        rWeek, (sizeof(rWeek) / sizeof(rWeek[0])), sizeof(rWeek[0]));
    TEST_ASSERT_EQUAL_INT(0, ret);

    /* read value */
    ret = bJsonGetStringArray(tree, kWeek, bWeek, 7, 16);
    TEST_ASSERT_EQUAL_INT(0, ret);

    cJSON_Delete(tree);

    TEST_ASSERT_EQUAL_STRING_ARRAY(vWeek, bWeek, 7);
}

void test_bJsonGetDoubleArray(void)
{
    int ret;
    char *kResolution = "resolutions";
    const int arrayLen = 4;
    const double vResolution[4] = {
        1920.868,
        1300.794,
        1080.482,
        760.517
    };

    /* create object */
    cJSON *tree = cJSON_CreateObject();
    cJSON *array = cJSON_CreateDoubleArray(vResolution, sizeof(vResolution) / sizeof(vResolution[0]));
    cJSON_AddItemToObject(tree, kResolution, array);

    /* read value */
    double rResolution[4] = {0};
    ret = bJsonGetDoubleArray(tree, kResolution, rResolution, arrayLen);
    TEST_ASSERT_EQUAL_INT(0, ret);

    cJSON_Delete(tree);

    TEST_ASSERT_EQUAL_DOUBLE_ARRAY(vResolution, rResolution, 4);
}

void test_bJsonGetFloatArray(void)
{
    int ret;
    char *kResolution = "resolutions";
    const int arrayLen = 4;
    const float vResolution[4] = {
        1920.868,
        1300.794,
        1080.482,
        760.517
    };

    /* create object */
    cJSON *tree = cJSON_CreateObject();
    cJSON *array = cJSON_CreateFloatArray(vResolution, sizeof(vResolution) / sizeof(vResolution[0]));
    cJSON_AddItemToObject(tree, kResolution, array);

    /* read value */
    float rResolution[4] = {0};
    ret = bJsonGetFloatArray(tree, kResolution, rResolution, arrayLen);
    TEST_ASSERT_EQUAL_INT(0, ret);

    cJSON_Delete(tree);

    TEST_ASSERT_EQUAL_FLOAT_ARRAY(vResolution, rResolution, 4);
}

void test_bJsonGetIntegerArray(void)
{
    int ret;
    char *kResolution = "resolutions";
    const int arrayLen = 4;
    const int vResolution[4] = {
        1920,
        1300,
        1080,
        760
    };

    /* create object */
    cJSON *tree = cJSON_CreateObject();
    cJSON *array = cJSON_CreateIntArray(vResolution, sizeof(vResolution) / sizeof(vResolution[0]));
    cJSON_AddItemToObject(tree, kResolution, array);

    /* read value */
    int rResolution[4] = {0};
    ret = bJsonGetIntegerArray(tree, kResolution, rResolution, arrayLen);
    TEST_ASSERT_EQUAL_INT(0, ret);

    cJSON_Delete(tree);

    TEST_ASSERT_EQUAL_INT32_ARRAY(vResolution, rResolution, 4);
}

void test_bJsonUpdateString(void)
{
    int ret;
    char *kCity = "City"; char *vCity = "ShenZhen"; char *uCity = "ShangHai"; char rCity[64] = {0};

    /* create object */
    cJSON *tree = cJSON_CreateObject();
    cJSON_AddStringToObject(tree, kCity, vCity);

    /* update value */
    ret = bJsonUpdateString(tree, kCity, uCity);
    TEST_ASSERT_EQUAL_INT(0, ret);

    /* read value */
    ret = bJsonGetString(tree, kCity, rCity, sizeof(rCity));
    TEST_ASSERT_EQUAL_INT(0, ret);

    cJSON_Delete(tree);

    TEST_ASSERT_EQUAL_STRING(uCity, rCity);
}

void test_bJsonUpdateDouble(void)
{
    int ret;
    char *kWidth = "Width"; double vWidth = 1920.026; double uWidth = 1080.251; double rWidth = 0;

    /* create object */
    cJSON *tree = cJSON_CreateObject();
    cJSON_AddNumberToObject(tree, kWidth, vWidth);

    /* update value */
    ret = bJsonUpdateDouble(tree, kWidth, uWidth);
    TEST_ASSERT_EQUAL_INT(0, ret);

    /* read value */
    ret = bJsonGetDouble(tree, kWidth, &rWidth);
    TEST_ASSERT_EQUAL_INT(0, ret);

    cJSON_Delete(tree);

    TEST_ASSERT_EQUAL_MEMORY(&uWidth, &rWidth, sizeof(double));
    TEST_ASSERT_EQUAL_DOUBLE(uWidth, rWidth);
}

void test_bJsonUpdateFloat(void)
{
    int ret;
    char *kWidth = "Width"; float vWidth = 1920.026; float uWidth = 1080.251; float rWidth = 0;

    /* create object */
    cJSON *tree = cJSON_CreateObject();
    cJSON_AddNumberToObject(tree, kWidth, vWidth);

    /* update value */
    ret = bJsonUpdateFloat(tree, kWidth, uWidth);
    TEST_ASSERT_EQUAL_INT(0, ret);

    /* read value */
    ret = bJsonGetFloat(tree, kWidth, &rWidth);
    TEST_ASSERT_EQUAL_INT(0, ret);

    cJSON_Delete(tree);

    TEST_ASSERT_EQUAL_MEMORY(&uWidth, &rWidth, sizeof(float));
    TEST_ASSERT_EQUAL_FLOAT(uWidth, rWidth);
}

void test_bJsonUpdateInteger(void)
{
    int ret;
    char *kWidth = "Width"; int vWidth = 1920.026; int uWidth = 1080.251; int rWidth = 0;

    /* create object */
    cJSON *tree = cJSON_CreateObject();
    cJSON_AddNumberToObject(tree, kWidth, vWidth);

    /* update value */
    ret = bJsonUpdateInteger(tree, kWidth, uWidth);
    TEST_ASSERT_EQUAL_INT(0, ret);

    /* read value */
    ret = bJsonGetInteger(tree, kWidth, &rWidth);
    TEST_ASSERT_EQUAL_INT(0, ret);

    cJSON_Delete(tree);

    TEST_ASSERT_EQUAL_MEMORY(&uWidth, &rWidth, sizeof(int));
    TEST_ASSERT_EQUAL_INT32(uWidth, rWidth);
}



void test_bJsonUpdateStringArray(void)
{
    int ret;
    char *kWeek = "week";
    const char *vWeek[7] = {
        "Sunday",
        "Monday",
        "Tuesday",
        "Wednesday",
        "Thursday",
        "Friday",
        "Saturday"
    };

    char *uWeek[7] = {
        "Sunday_1",
        "Monday_2",
        "Tuesday_3",
        "Wednesday_4",
        "Thursday_5",
        "Friday_6",
        "Saturday_7"
    };

    /* create object */
    cJSON *tree = cJSON_CreateObject();
    cJSON *array = cJSON_CreateStringArray(vWeek, sizeof(vWeek) / sizeof(vWeek[0]));
    cJSON_AddItemToObject(tree, kWeek, array);

    char rWeek[7][16] = {0};
    char *bWeek[7] = {0};

    ret = bJsonStrPtrToArray(bWeek, (sizeof(bWeek) / sizeof(bWeek[0])),
        rWeek, (sizeof(rWeek) / sizeof(rWeek[0])), sizeof(rWeek[0]));
    TEST_ASSERT_EQUAL_INT(0, ret);

    /* update value */
    ret = bJsonUpdateStringArray(tree, kWeek, uWeek, 7);
    TEST_ASSERT_EQUAL_INT(0, ret);

    /* read value */
    ret = bJsonGetStringArray(tree, kWeek, bWeek, 7, 16);
    TEST_ASSERT_EQUAL_INT(0, ret);

    cJSON_Delete(tree);

    TEST_ASSERT_EQUAL_STRING_ARRAY(uWeek, bWeek, 7);
}

void test_bJsonUpdateDoubleArray(void)
{
    int ret;
    char *kResolution = "resolutions";
    const int arrayLen = 4;
    const double vResolution[4] = {
        1920.868,
        1300.794,
        1080.482,
        760.517
    };
    double uResolution[4] = {
        19200.868,
        13000.794,
        10800.482,
        7600.517
    };
    double rResolution[4] = {0};

    /* create object */
    cJSON *tree = cJSON_CreateObject();
    cJSON *array = cJSON_CreateDoubleArray(vResolution, sizeof(vResolution) / sizeof(vResolution[0]));
    cJSON_AddItemToObject(tree, kResolution, array);

    /* update value */
    ret = bJsonUpdateDoubleArray(tree, kResolution, uResolution, arrayLen);
    TEST_ASSERT_EQUAL_INT(0, ret);

    /* read value */
    ret = bJsonGetDoubleArray(tree, kResolution, rResolution, arrayLen);
    TEST_ASSERT_EQUAL_INT(0, ret);

    cJSON_Delete(tree);

    TEST_ASSERT_EQUAL_DOUBLE_ARRAY(uResolution, rResolution, 4);
}

void test_bJsonUpdateFloatArray(void)
{
    int ret;
    char *kResolution = "resolutions";
    const int arrayLen = 4;
    const float vResolution[4] = {
        1920.868,
        1300.794,
        1080.482,
        760.517
    };
    float uResolution[4] = {
        19200.868,
        13000.794,
        10800.482,
        7600.517
    };
    float rResolution[4] = {0};

    /* create object */
    cJSON *tree = cJSON_CreateObject();
    cJSON *array = cJSON_CreateFloatArray(vResolution, sizeof(vResolution) / sizeof(vResolution[0]));
    cJSON_AddItemToObject(tree, kResolution, array);

    /* update value */
    ret = bJsonUpdateFloatArray(tree, kResolution, uResolution, arrayLen);
    TEST_ASSERT_EQUAL_INT(0, ret);

    /* read value */
    ret = bJsonGetFloatArray(tree, kResolution, rResolution, arrayLen);
    TEST_ASSERT_EQUAL_INT(0, ret);

    cJSON_Delete(tree);

    TEST_ASSERT_EQUAL_FLOAT_ARRAY(uResolution, rResolution, 4);
}

void test_bJsonUpdateIntegerArray(void)
{
    int ret;
    char *kResolution = "resolutions";
    const int arrayLen = 4;
    const int vResolution[4] = {
        1920,
        1300,
        1080,
        7607
    };
    int uResolution[4] = {
        192000,
        130000,
        108000,
        76000
    };
    int rResolution[4] = {0};

    /* create object */
    cJSON *tree = cJSON_CreateObject();
    cJSON *array = cJSON_CreateIntArray(vResolution, sizeof(vResolution) / sizeof(vResolution[0]));
    cJSON_AddItemToObject(tree, kResolution, array);

    /* update value */
    ret = bJsonUpdateIntegerArray(tree, kResolution, uResolution, arrayLen);
    TEST_ASSERT_EQUAL_INT(0, ret);

    /* read value */
    ret = bJsonGetIntegerArray(tree, kResolution, rResolution, arrayLen);
    TEST_ASSERT_EQUAL_INT(0, ret);

    cJSON_Delete(tree);

    TEST_ASSERT_EQUAL_INT32_ARRAY(uResolution, rResolution, 4);
}
