/*******************************************************************************
* ZHT Communication & technology Co.Ltd 2019-2029 Copyright reserved
* File : test.c
* Function : bJson test;
* Author : wuliuzhi
* Created on: 2025.07.02
*
* Modification history:
* Ver Who Date Changes
* ------------------------------------------------------------------------------
* V1.0 wuliuzhi 2025/07/02 First release
********************************************************************************/
#include "testApi.h"


int main(void)
{
    UNITY_BEGIN();

    RUN_TEST(test_bJsonExportFile);
    RUN_TEST(test_bJsonImportFile);

    RUN_TEST(test_bJsonGetString);
    RUN_TEST(test_bJsonGetDouble);
    RUN_TEST(test_bJsonGetFloat);
    RUN_TEST(test_bJsonGetInteger);

    RUN_TEST(test_bJsonGetStringArray);
    RUN_TEST(test_bJsonGetDoubleArray);
    RUN_TEST(test_bJsonGetFloatArray);
    RUN_TEST(test_bJsonGetIntegerArray);

    RUN_TEST(test_bJsonUpdateString);
    RUN_TEST(test_bJsonUpdateDouble);
    RUN_TEST(test_bJsonUpdateFloat);
    RUN_TEST(test_bJsonUpdateInteger);

    RUN_TEST(test_bJsonUpdateStringArray);
    RUN_TEST(test_bJsonUpdateDoubleArray);
    RUN_TEST(test_bJsonUpdateFloatArray);
    RUN_TEST(test_bJsonUpdateIntegerArray);

    UNITY_END();

    return 0;
}
