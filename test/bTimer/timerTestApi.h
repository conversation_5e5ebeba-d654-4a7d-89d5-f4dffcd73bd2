/*******************************************************************************
* ZHT Communication & technology Co.Ltd 2019-2029 Copyright reserved
* File : testApi.h
* Function : bTimer test;
* Author : yangzuogui
* Created on: 2025.07.07
*
* Modification history:
* Ver Who Date Changes
* ------------------------------------------------------------------------------
* V1.0 yangzuogui 2025/07/07 First release
********************************************************************************/
#ifndef __TIMER_TEST_API_H__
#define __TIMER_TEST_API_H__
#include <unistd.h>
#include "unity.h"
#include "bTimer.h"

void test_bTimerCreate(void);

void test_bTimerAddTask(void);

void test_bTimerDeleteTask(void);

void test_bTimerRelease(void);

void test_bTimerGetRemainingCount(void);

void test_bTimerGetRemainingTime(void);

void test_bTimerGetNextTriggerTime(void);

void test_bTimerGetInterval(void);

void test_bTimerGetTaskCnt(void);

#endif