/*******************************************************************************
* ZHT Communication & technology Co.Ltd 2019-2029 Copyright reserved
* File : timerTestApi.c
* Function : bTimer test;
* Author : yangzuogui
* Created on: 2025.07.07
*
* Modification history:
* Ver Who Date Changes
* ------------------------------------------------------------------------------
* V1.0 yangzuogui 2025/07/07 First release
********************************************************************************/
#include "timerTestApi.h"

static void test_bTimerAddTask_cb(void *data) 
{
    int *pId = (int*)data;
    *pId += 1;
}

void test_bTimerCreate(void)
{
    // success
    bTimer_t *ptTimer = bTimerCreate(5);
    TEST_ASSERT_NOT_NULL(ptTimer);
    bTimerRelease(ptTimer);

    // error params
    ptTimer = bTimerCreate(0);
    TEST_ASSERT_NULL(ptTimer);
}

void test_bTimerAddTask(void)
{
    bTimer_t *ptTimer = bTimerCreate(5);
    TEST_ASSERT_NOT_NULL(ptTimer);

    int sum = 0;
    // success
    int timer_id1 = bTimerAddTask(ptTimer, 50, 5, test_bTimerAddTask_cb, &sum, NULL);
    TEST_ASSERT_GREATER_THAN_INT32(0, timer_id1);
    sleep(3);

    // count right
    TEST_ASSERT_EQUAL(4, sum);

    // success
    int timer_id2 = bTimerAddTask(ptTimer, 1000, 5, test_bTimerAddTask_cb, &sum, NULL);
    TEST_ASSERT_GREATER_THAN_INT32(0, timer_id2);

    // success
    int timer_id3 = bTimerAddTask(ptTimer, 1000, 5, test_bTimerAddTask_cb, &sum, NULL);
    TEST_ASSERT_GREATER_THAN_INT32(0, timer_id3);

    // success
    int timer_id4 = bTimerAddTask(ptTimer, 1000, 5, test_bTimerAddTask_cb, &sum, NULL);
    TEST_ASSERT_GREATER_THAN_INT32(0, timer_id4);

    // success
    int timer_id5 = bTimerAddTask(ptTimer, 1000, 5, test_bTimerAddTask_cb, &sum, NULL);
    TEST_ASSERT_GREATER_THAN_INT32(0, timer_id5);

    // success
    int timer_id6 = bTimerAddTask(ptTimer, 1000, 5, test_bTimerAddTask_cb, &sum, NULL);
    TEST_ASSERT_GREATER_THAN_INT32(0, timer_id6);

    // fail, task out of MAX count limit
    int timer_id7 = bTimerAddTask(ptTimer, 1000, 5, test_bTimerAddTask_cb, &sum, NULL);
    TEST_ASSERT_EQUAL_INT32(-5, timer_id7);

    bTimerRelease(ptTimer);
}

void test_bTimerDeleteTask(void)
{
    bTimer_t *ptTimer = bTimerCreate(5);
    TEST_ASSERT_NOT_NULL(ptTimer);
    int ret = 0;
    int sum = 0;

    // success if greater than 0
    int timer_id1 = bTimerAddTask(ptTimer, 50, 5, test_bTimerAddTask_cb, &sum, NULL);
    TEST_ASSERT_GREATER_THAN_INT32(0, timer_id1);

    // success if greater than 0
    int timer_id2 = bTimerAddTask(ptTimer, 50, 5, test_bTimerAddTask_cb, &sum, NULL);
    TEST_ASSERT_GREATER_THAN_INT32(0, timer_id2);

    // success if equal 0
    ret = bTimerDeleteTask(ptTimer, timer_id1);
    TEST_ASSERT_EQUAL(0, ret);

    // not allow to delete twice, should be -1
    ret = bTimerDeleteTask(ptTimer, timer_id1);
    TEST_ASSERT_EQUAL(-1, ret);

    // error params, should be -1
    ret = bTimerDeleteTask(NULL, timer_id1);
    TEST_ASSERT_EQUAL(-1, ret);

    // error params, should be -1
    ret = bTimerDeleteTask(ptTimer, 0);
    TEST_ASSERT_EQUAL(-1, ret);

    // error params, should be -1
    ret = bTimerDeleteTask(ptTimer, 1);
    TEST_ASSERT_EQUAL(-1, ret);

    // success, should be 0
    ret = bTimerDeleteTask(ptTimer, timer_id2);
    TEST_ASSERT_EQUAL(0, ret);

    bTimerRelease(ptTimer);
}


void test_bTimerRelease(void)
{
    bTimer_t *ptTimer = bTimerCreate(5);
    TEST_ASSERT_NOT_NULL(ptTimer);

    bTimerRelease(ptTimer);

    // failed 
    int timer_id1 = bTimerAddTask(ptTimer, 50, 5, test_bTimerAddTask_cb, NULL, NULL);
    TEST_ASSERT_EQUAL(-1, timer_id1);
}

void test_bTimerGetRemainingCount(void)
{
    bTimer_t *ptTimer = bTimerCreate(5);
    TEST_ASSERT_NOT_NULL(ptTimer);
    int ret = 0;
    int sum = 0;

    // success if greater than 0
    int timer_id1 = bTimerAddTask(ptTimer, 1000, 5, test_bTimerAddTask_cb, &sum, NULL);
    TEST_ASSERT_GREATER_THAN_INT32(0, timer_id1);

    int remainingCnt = 0;
    // success if equal 0
    ret = bTimerGetRemainingCount(ptTimer, timer_id1, &remainingCnt);
    TEST_ASSERT_EQUAL(0, ret);

    // should be greate than 1, almost 5
    TEST_ASSERT_GREATER_THAN_INT32(1, remainingCnt);

    bTimerRelease(ptTimer);
}


void test_bTimerGetRemainingTime(void)
{
    bTimer_t *ptTimer = bTimerCreate(5);
    TEST_ASSERT_NOT_NULL(ptTimer);
    int ret = 0;
    int sum = 0;

    // success if greater than 0
    int timer_id1 = bTimerAddTask(ptTimer, 2000, 5, test_bTimerAddTask_cb, &sum, NULL);
    TEST_ASSERT_GREATER_THAN_INT32(0, timer_id1);

    uint64_t remainingMs = 0;
    // success if equal 0
    ret = bTimerGetRemainingTime(ptTimer, timer_id1, &remainingMs);
    TEST_ASSERT_EQUAL(0, ret);

    // should be greate than 100, almost 2000
    TEST_ASSERT_GREATER_THAN_UINT64(100, remainingMs);

    // FAIL
    ret = bTimerGetRemainingTime(NULL, timer_id1, &remainingMs);
    TEST_ASSERT_EQUAL(-1, ret);

    // FAIL
    ret = bTimerGetRemainingTime(ptTimer, 0, &remainingMs);
    TEST_ASSERT_EQUAL(-1, ret);

    // FAIL
    ret = bTimerGetRemainingTime(ptTimer, timer_id1, NULL);
    TEST_ASSERT_EQUAL(-1, ret);

    bTimerRelease(ptTimer);
}


void test_bTimerGetNextTriggerTime(void)
{
    bTimer_t *ptTimer = bTimerCreate(5);
    TEST_ASSERT_NOT_NULL(ptTimer);
    int ret = 0;
    int sum = 0;

    // success if greater than 0
    int timer_id1 = bTimerAddTask(ptTimer, 2000, 5, test_bTimerAddTask_cb, &sum, NULL);
    TEST_ASSERT_GREATER_THAN_INT32(0, timer_id1);

    uint64_t nextTriggerMs = 0;
    // success if equal 0
    ret = bTimerGetNextTriggerTime(ptTimer, timer_id1, &nextTriggerMs);
    TEST_ASSERT_EQUAL(0, ret);

    // should be greate than 100, almost 2000
    TEST_ASSERT_GREATER_THAN_UINT64(100, nextTriggerMs);

    // FAIL
    ret = bTimerGetNextTriggerTime(NULL, timer_id1, &nextTriggerMs);
    TEST_ASSERT_EQUAL(-1, ret);

    // FAIL
    ret = bTimerGetNextTriggerTime(ptTimer, 0, &nextTriggerMs);
    TEST_ASSERT_EQUAL(-1, ret);

    // FAIL
    ret = bTimerGetNextTriggerTime(ptTimer, timer_id1, NULL);
    TEST_ASSERT_EQUAL(-1, ret);

    bTimerRelease(ptTimer);
}


void test_bTimerGetInterval(void)
{
    bTimer_t *ptTimer = bTimerCreate(5);
    TEST_ASSERT_NOT_NULL(ptTimer);
    int ret = 0;
    int sum = 0;

    // success if greater than 0
    int timer_id1 = bTimerAddTask(ptTimer, 2000, 5, test_bTimerAddTask_cb, &sum, NULL);
    TEST_ASSERT_GREATER_THAN_INT32(0, timer_id1);

    uint64_t intervalMs = 0;
    // success if equal 0
    ret = bTimerGetInterval(ptTimer, timer_id1, &intervalMs);
    TEST_ASSERT_EQUAL(0, ret);

    // should be equal 2000
    TEST_ASSERT_EQUAL_UINT64(2000, intervalMs);

    // FAIL
    ret = bTimerGetInterval(NULL, timer_id1, &intervalMs);
    TEST_ASSERT_EQUAL(-1, ret);

    // FAIL
    ret = bTimerGetInterval(ptTimer, 0, &intervalMs);
    TEST_ASSERT_EQUAL(-1, ret);

    // FAIL
    ret = bTimerGetInterval(ptTimer, timer_id1, NULL);
    TEST_ASSERT_EQUAL(-1, ret);

    bTimerRelease(ptTimer);
}

void test_bTimerGetTaskCnt(void)
{
    bTimer_t *ptTimer = bTimerCreate(5);
    TEST_ASSERT_NOT_NULL(ptTimer);
    int ret = 0;
    int sum = 0;

    // success if greater than 0
    int timer_id1 = bTimerAddTask(ptTimer, 2000, 5, test_bTimerAddTask_cb, &sum, NULL);
    TEST_ASSERT_GREATER_THAN_INT32(0, timer_id1);

    // success if equal 1
    ret = bTimerGetTaskCnt(ptTimer);
    TEST_ASSERT_EQUAL_INT32(1, ret);

    // success if greater than 0
    int timer_id2 = bTimerAddTask(ptTimer, 2000, 5, test_bTimerAddTask_cb, &sum, NULL);
    TEST_ASSERT_GREATER_THAN_INT32(0, timer_id2);

    // success if equal 2
    ret = bTimerGetTaskCnt(ptTimer);
    TEST_ASSERT_EQUAL_INT32(2, ret);

    // FAIL
    ret = bTimerGetTaskCnt(NULL);
    TEST_ASSERT_EQUAL(-1, ret);

    bTimerRelease(ptTimer);
}