/*
 * *****************************************************************************************
 * ZHT Communication & technology Co.Ltd 2019-2029 Copyright Reserved.
 * @FilePath: /BSL/test/bTimer/timerTest.c
 * @Description: 
 * @Author: yangzuogui <EMAIL>
 * @Date: 2025-07-07 17:13:58
 * @LastEditors: yangzuogui <EMAIL>
 * @LastEditTime: 2025-07-09 10:44:05
 * *****************************************************************************************
 */
#include "timerTestApi.h"


int main() 
{
    UNITY_BEGIN();

    RUN_TEST(test_bTimerCreate);
    RUN_TEST(test_bTimerAddTask);
    RUN_TEST(test_bTimerDeleteTask);
    RUN_TEST(test_bTimerRelease);
    RUN_TEST(test_bTimerGetRemainingCount);
    RUN_TEST(test_bTimerGetRemainingTime);
    RUN_TEST(test_bTimerGetNextTriggerTime);
    RUN_TEST(test_bTimerGetInterval);
    RUN_TEST(test_bTimerGetTaskCnt);

    UNITY_END();
    return 0;
}