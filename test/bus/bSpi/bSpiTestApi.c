#include "bSpiTestApi.h"

// 测试固件
static bSpiDev_t *spi_dev = NULL;
static const char *TEST_DEV_PATH = "/dev/spidev0.0";
static const uint32_t TEST_SPEED = 1000000;
static const uint8_t TEST_BITS = 8;
static const bSpiMode_e TEST_MODE = SPI_MODE_0;

// 模拟上下文结构体
typedef struct {
    int return_value;
    uint8_t mode_value;
    uint8_t bits_value;
    uint32_t speed_value;
    size_t transfer_len;
} IoctlMockContext;

static IoctlMockContext ioctl_mock_ctx;

// 模拟open函数
int __wrap_open(const char *pathname, int flags) {
    // 模拟打开失败
    if (strcmp(pathname, "/dev/fail") == 0) {
        errno = ENOENT;
        return -1;
    }
    // 正常打开
    return 10;  // 返回有效的文件描述符
}

int __wrap_close(int fd) {
    return 0; // 总是成功
}

// 模拟ioctl函数
int __wrap_ioctl(int fd, unsigned long request, ...) {
    va_list args;
    va_start(args, request);
    
    int ret = -1;
    uint8_t *u8_ptr;
    uint32_t *u32_ptr;
    struct spi_ioc_transfer *transfer;
    
    switch (request) {
        case SPI_IOC_WR_MODE:
        case SPI_IOC_RD_MODE:
            u8_ptr = va_arg(args, uint8_t*);
            if (request == SPI_IOC_WR_MODE) {
                // 保存写入的值
                ioctl_mock_ctx.mode_value = *u8_ptr;
            } else {
                // 返回模拟的值
                *u8_ptr = ioctl_mock_ctx.mode_value;
            }
            ret = ioctl_mock_ctx.return_value;
            break;
            
        case SPI_IOC_WR_BITS_PER_WORD:
        case SPI_IOC_RD_BITS_PER_WORD:
            u8_ptr = va_arg(args, uint8_t*);
            if (request == SPI_IOC_WR_BITS_PER_WORD) {
                ioctl_mock_ctx.bits_value = *u8_ptr;
            } else {
                *u8_ptr = ioctl_mock_ctx.bits_value;
            }
            ret = ioctl_mock_ctx.return_value;
            break;
            
        case SPI_IOC_WR_MAX_SPEED_HZ:
        case SPI_IOC_RD_MAX_SPEED_HZ:
            u32_ptr = va_arg(args, uint32_t*);
            if (request == SPI_IOC_WR_MAX_SPEED_HZ) {
                ioctl_mock_ctx.speed_value = *u32_ptr;
            } else {
                *u32_ptr = ioctl_mock_ctx.speed_value;
            }
            ret = ioctl_mock_ctx.return_value;
            break;
            
        case SPI_IOC_MESSAGE(1):
            transfer = va_arg(args, struct spi_ioc_transfer*);
            // 模拟接收数据
            if (transfer->rx_buf) {
                memset(transfer->rx_buf, 0xAA, transfer->len);
            }
            ret = ioctl_mock_ctx.transfer_len;
            break;
            
        default:
            ret = -1;
            errno = EINVAL;
            break;
    }
    
    va_end(args);
    return ret;
}

// 测试初始化
void setUp(void) 
{
    // 重置模拟上下文
    memset(&ioctl_mock_ctx, 0, sizeof(ioctl_mock_ctx));
    
    // 设置模拟成功值
    ioctl_mock_ctx.return_value = 0;
    ioctl_mock_ctx.mode_value = TEST_MODE;
    ioctl_mock_ctx.bits_value = TEST_BITS;
    ioctl_mock_ctx.speed_value = TEST_SPEED;
    
    spi_dev = bSpiOpen(TEST_DEV_PATH, TEST_MODE, TEST_SPEED, TEST_BITS);
}

// 测试清理
void tearDown(void) 
{
    if (spi_dev) {
        bSpiClose(spi_dev);
        spi_dev = NULL;
    }
}

// 测试用例1: 成功打开SPI设备
void test_spiOpenSuccess(void) 
{
    TEST_ASSERT_NOT_NULL(spi_dev);
    TEST_ASSERT_EQUAL_STRING(TEST_DEV_PATH, bSpiGetDevPath(spi_dev));

    bSpiMode_e mode = 0;
    int ret = bSpiGetMode(spi_dev, &mode);
    TEST_ASSERT_EQUAL(TEST_MODE, mode);

    uint32_t speed = 0;
    ret = bSpiGetSpeed(spi_dev, &speed);
    TEST_ASSERT_EQUAL(TEST_SPEED, speed);

    uint8_t bits = 0;
    ret = bSpiGetBits(spi_dev, &bits);
    TEST_ASSERT_EQUAL(TEST_BITS, bits);
}

// 测试用例2: 打开设备失败处理
void test_spiOpenFailure(void) 
{
    // 测试无效路径
    bSpiDev_t *dev = bSpiOpen(NULL, TEST_MODE, TEST_SPEED, TEST_BITS);
    TEST_ASSERT_NULL(dev);
    
    // 测试无效位宽
    dev = bSpiOpen(TEST_DEV_PATH, TEST_MODE, TEST_SPEED, 7);
    TEST_ASSERT_NULL(dev);
    
    // 测试打开失败
    ioctl_mock_ctx.return_value = -1; // 模拟失败
    dev = bSpiOpen("/dev/fail", TEST_MODE, TEST_SPEED, TEST_BITS);
    TEST_ASSERT_NULL(dev);
}

// 测试用例3: 模式设置和获取
void test_spiModeSetGet(void) 
{
    bSpiMode_e mode;
    
    // 测试获取模式
    TEST_ASSERT_EQUAL(0, bSpiGetMode(spi_dev, &mode));
    TEST_ASSERT_EQUAL(TEST_MODE, mode);
    
    // 测试设置模式
    TEST_ASSERT_EQUAL(0, bSpiSetMode(spi_dev, SPI_MODE_1));
    TEST_ASSERT_EQUAL(0, bSpiGetMode(spi_dev, &mode));
    TEST_ASSERT_EQUAL(SPI_MODE_1, mode);
    
    // 测试无效参数
    TEST_ASSERT_EQUAL(-1, bSpiSetMode(NULL, SPI_MODE_0));
    TEST_ASSERT_EQUAL(-1, bSpiGetMode(NULL, &mode));
    TEST_ASSERT_EQUAL(-1, bSpiGetMode(spi_dev, NULL));
    
    // 测试设置失败
    ioctl_mock_ctx.return_value = -1;
    TEST_ASSERT_EQUAL(-1, bSpiSetMode(spi_dev, SPI_MODE_2));
    ioctl_mock_ctx.return_value = 0; // 恢复
}

// 测试用例4: 速率设置和获取
void test_spiSpeedSetGet(void) 
{
    uint32_t speed;
    
    // 测试获取速率
    TEST_ASSERT_EQUAL(0, bSpiGetSpeed(spi_dev, &speed));
    TEST_ASSERT_EQUAL(TEST_SPEED, speed);
    
    // 测试设置速率
    TEST_ASSERT_EQUAL(0, bSpiSetSpeed(spi_dev, 2000000));
    TEST_ASSERT_EQUAL(0, bSpiGetSpeed(spi_dev, &speed));
    TEST_ASSERT_EQUAL(2000000, speed);
    
    // 测试无效参数
    TEST_ASSERT_EQUAL(-1, bSpiSetSpeed(NULL, 1000000));
    TEST_ASSERT_EQUAL(-1, bSpiGetSpeed(NULL, &speed));
    TEST_ASSERT_EQUAL(-1, bSpiGetSpeed(spi_dev, NULL));
    
    // 测试设置失败
    ioctl_mock_ctx.return_value = -1;
    TEST_ASSERT_EQUAL(-1, bSpiSetSpeed(spi_dev, 500000));
    ioctl_mock_ctx.return_value = 0; // 恢复
}

// 测试用例5: 位宽设置和获取
void test_spiBitsSetGet(void) 
{
    uint8_t bits;
    
    // 测试获取位宽
    TEST_ASSERT_EQUAL(0, bSpiGetBits(spi_dev, &bits));
    TEST_ASSERT_EQUAL(TEST_BITS, bits);
    
    // 测试设置位宽
    TEST_ASSERT_EQUAL(0, bSpiSetBits(spi_dev, 16));
    TEST_ASSERT_EQUAL(0, bSpiGetBits(spi_dev, &bits));
    TEST_ASSERT_EQUAL(16, bits);
    
    // 测试无效参数
    TEST_ASSERT_EQUAL(-1, bSpiSetBits(NULL, 8));
    TEST_ASSERT_EQUAL(-1, bSpiGetBits(NULL, &bits));
    TEST_ASSERT_EQUAL(-1, bSpiGetBits(spi_dev, NULL));
    TEST_ASSERT_EQUAL(-1, bSpiSetBits(spi_dev, 7)); // 无效位宽
    
    // // 测试设置失败
    ioctl_mock_ctx.return_value = -1;
    TEST_ASSERT_EQUAL(-1, bSpiSetBits(spi_dev, 32));
    ioctl_mock_ctx.return_value = 0; // 恢复
}

// 测试用例6: 全双工数据传输
void test_spiTransferFullDuplex(void) 
{
    uint8_t tx_buf[4] = {0x01, 0x02, 0x03, 0x04};
    uint8_t rx_buf[4] = {0};
    
    // 设置模拟传输成功
    ioctl_mock_ctx.transfer_len = sizeof(tx_buf);
    
    int result = bSpiTransfer(spi_dev, tx_buf, rx_buf, sizeof(tx_buf));
    TEST_ASSERT_EQUAL(sizeof(tx_buf), result);
    
    // 验证接收数据
    for (int i = 0; i < sizeof(rx_buf); i++) {
        TEST_ASSERT_EQUAL_HEX8(0xAA, rx_buf[i]);
    }
    
    // 测试传输失败
    ioctl_mock_ctx.transfer_len = -1;
    result = bSpiTransfer(spi_dev, tx_buf, rx_buf, sizeof(tx_buf));
    TEST_ASSERT_EQUAL(-1, result);
    ioctl_mock_ctx.transfer_len = 4; // 恢复
}

// 测试用例7: 单独发送和接收
void test_spiSendReceive(void) 
{
    uint8_t tx_buf[3] = {0xAA, 0xBB, 0xCC};
    uint8_t rx_buf[3] = {0};
    
    // 测试发送
    ioctl_mock_ctx.transfer_len = sizeof(tx_buf);
    int send_result = bSpiSend(spi_dev, tx_buf, sizeof(tx_buf));
    TEST_ASSERT_EQUAL(sizeof(tx_buf), send_result);
    
    // 测试接收
    ioctl_mock_ctx.transfer_len = sizeof(rx_buf);
    int recv_result = bSpiReceive(spi_dev, rx_buf, sizeof(rx_buf));
    TEST_ASSERT_EQUAL(sizeof(rx_buf), recv_result);
    
    // 验证接收数据
    for (int i = 0; i < sizeof(rx_buf); i++) {
        TEST_ASSERT_EQUAL_HEX8(0xAA, rx_buf[i]);
    }
    
    // 测试发送失败
    ioctl_mock_ctx.transfer_len = -1;
    send_result = bSpiSend(spi_dev, tx_buf, sizeof(tx_buf));
    TEST_ASSERT_EQUAL(-1, send_result);
    
    // 测试接收失败
    recv_result = bSpiReceive(spi_dev, rx_buf, sizeof(rx_buf));
    TEST_ASSERT_EQUAL(-1, recv_result);
    ioctl_mock_ctx.transfer_len = 3; // 恢复
}

// 测试用例8: 无效参数处理
void test_spiInvalidParameters(void) 
{
    uint8_t buf[4] = {0};
    
    // 无效设备指针
    TEST_ASSERT_EQUAL(-1, bSpiTransfer(NULL, buf, buf, 4));
    TEST_ASSERT_EQUAL(-1, bSpiSend(NULL, buf, 4));
    TEST_ASSERT_EQUAL(-1, bSpiReceive(NULL, buf, 4));
    
    // 无效缓冲区
    TEST_ASSERT_EQUAL(-1, bSpiTransfer(spi_dev, NULL, NULL, 4));
    TEST_ASSERT_EQUAL(-1, bSpiSend(spi_dev, NULL, 4));
    TEST_ASSERT_EQUAL(-1, bSpiReceive(spi_dev, NULL, 4));
    
    // 零长度传输
    TEST_ASSERT_EQUAL(-1, bSpiTransfer(spi_dev, buf, buf, 0));
    TEST_ASSERT_EQUAL(-1, bSpiSend(spi_dev, buf, 0));
    TEST_ASSERT_EQUAL(-1, bSpiReceive(spi_dev, buf, 0));
    
    // 仅发送时接收缓冲区非空
    uint8_t dummy[4];
    TEST_ASSERT_EQUAL(4, bSpiSend(spi_dev, buf, 4)); // 应该正常工作
    
    // 仅接收时发送缓冲区非空
    TEST_ASSERT_EQUAL(4, bSpiReceive(spi_dev, dummy, 4)); // 应该正常工作
}

// 测试用例9: 边界条件测试
void test_spiBoundaryConditions(void) 
{
    // 测试最大传输长度
    #define MAX_LEN 4096
    uint8_t tx_buf[MAX_LEN];
    uint8_t rx_buf[MAX_LEN];
    memset(tx_buf, 0x55, MAX_LEN);
    
    ioctl_mock_ctx.transfer_len = MAX_LEN;
    int result = bSpiTransfer(spi_dev, tx_buf, rx_buf, MAX_LEN);
    TEST_ASSERT_EQUAL(MAX_LEN, result);
    
    // 测试超出最大长度
    ioctl_mock_ctx.transfer_len = -1;
    result = bSpiTransfer(spi_dev, tx_buf, rx_buf, MAX_LEN + 1);
    TEST_ASSERT_EQUAL(-1, result);
    ioctl_mock_ctx.transfer_len = MAX_LEN; // 恢复
    
    // 测试关闭空指针
    bSpiClose(NULL); // 不应崩溃
    
    // 测试重复关闭
    bSpiClose(spi_dev);
    spi_dev = NULL; // 防止tearDown再次关闭
}