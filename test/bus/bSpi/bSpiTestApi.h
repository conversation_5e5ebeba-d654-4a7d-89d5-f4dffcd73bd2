#ifndef __BSPI_TEST_API_H__
#define __BSPI_TEST_API_H__

#include "bSpi.h"
#include "unity.h"
#include <fcntl.h>
#include <unistd.h>
#include <linux/spi/spidev.h>
#include <errno.h>
#include <stdarg.h>

void test_spiOpenSuccess(void);
void test_spiOpenFailure(void);
void test_spiModeSetGet(void);
void test_spiSpeedSetGet(void);
void test_spiBitsSetGet(void);
void test_spiTransferFullDuplex(void);
void test_spiSendReceive(void);
void test_spiInvalidParameters(void);
void test_spiBoundaryConditions(void);

#endif