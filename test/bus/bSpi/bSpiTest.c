/*******************************************************************************
* ZHT Communication & technology Co.Ltd 2025-2035 Copyright reserved
* File : bSpiTest.c
* Function : bSpi test main entry
* Author : zhenmingwei
* Created on: 2025.07.30
*
* Modification history:
* Ver Who Date Changes
* ------------------------------------------------------------------------------
* V1.0 zhenmingwei 2025/07/30 First release
********************************************************************************/
#include "bSpiTestApi.h"
// 主函数 - 运行所有测试
int main(void) {
    UNITY_BEGIN();
    
    RUN_TEST(test_spiOpenSuccess);
    RUN_TEST(test_spiOpenFailure);
    RUN_TEST(test_spiModeSetGet);
    RUN_TEST(test_spiSpeedSetGet);
    RUN_TEST(test_spiBitsSetGet);
    RUN_TEST(test_spiTransferFullDuplex);
    RUN_TEST(test_spiSendReceive);
    RUN_TEST(test_spiInvalidParameters);
    RUN_TEST(test_spiBoundaryConditions);
    
    return UNITY_END();
}