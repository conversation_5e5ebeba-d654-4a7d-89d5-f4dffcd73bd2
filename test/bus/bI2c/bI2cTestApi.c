#include "bI2c.h"
#include "unity.h"

// 模拟系统调用
#include <stdarg.h>
#include <fcntl.h>
#include <unistd.h>
#include <sys/ioctl.h>
#include <linux/i2c-dev.h>

// 模拟硬件寄存器
static uint8_t mock_i2c_register[256];
static uint8_t mock_i2c_addr = 0x20;
static int mock_fd_counter = 100; // 模拟文件描述符

// 模拟设备路径
#define MOCK_DEV_PATH "/dev/i2c-0"

// 模拟系统调用
int __wrap_open(const char *pathname, int flags) {
    TEST_ASSERT_EQUAL_STRING(MOCK_DEV_PATH, pathname);
    return mock_fd_counter++;
}

int __wrap_close(int fd) {
    return 0; // 总是成功
}

// 模拟I2C传输
static int mock_i2c_transfer(struct i2c_rdwr_ioctl_data *data) {
    for (unsigned int i = 0; i < data->nmsgs; i++) {
        struct i2c_msg *msg = &data->msgs[i];
        
        if (msg->flags & I2C_M_RD) {
            // 模拟读操作 - 从寄存器返回数据
            for (unsigned int j = 0; j < msg->len; j++) {
                msg->buf[j] = mock_i2c_register[j];
            }
        } else {
            // 模拟写操作 - 保存到寄存器
            for (int j = 0; j < msg->len; j++) {
                mock_i2c_register[j] = msg->buf[j];
            }
        }
    }
    return 0;
}

// 模拟SMBus传输
static int mock_smbus_transfer(struct i2c_smbus_ioctl_data *data) {
    switch (data->size) {
        case I2C_SMBUS_QUICK:
            break;
        case I2C_SMBUS_BYTE:
            if (data->read_write == I2C_SMBUS_READ) {
                data->data->byte = mock_i2c_register[0];
            } else {
                mock_i2c_register[0] = data->data->byte;
            }
            break;
            
        case I2C_SMBUS_BYTE_DATA:
            if (data->read_write == I2C_SMBUS_WRITE) {
                mock_i2c_register[data->command] = data->data->byte;
            } else {
                data->data->byte = mock_i2c_register[data->command];
            }
            break;
            
        case I2C_SMBUS_BLOCK_DATA:
            if (data->read_write == I2C_SMBUS_WRITE) {
                uint8_t len = data->data->block[0];
                // mock_i2c_register[0] = len;
                for (int i = 0; i < len; i++) {
                    mock_i2c_register[i] = data->data->block[i];
                }
            } else {
                uint8_t len = mock_i2c_register[0];//mock_i2c_register[data->command];
                //data->data->block[0] = len;
                for (int i = 0; i < len; i++) {
                    data->data->block[i] = mock_i2c_register[i];
                }
            }
            break;
            
        default:
            return -1; // 模拟不支持的操作
    }
    return 0;
}

int __wrap_ioctl(int fd, unsigned long request, ...) {
    va_list args;
    va_start(args, request);
    
    switch(request) {
        case I2C_SLAVE: {
            uint8_t addr = va_arg(args, uint32_t);
            mock_i2c_addr = addr;
            return 0;
        }
        case I2C_FUNCS: {
            uint32_t *funcs = va_arg(args, uint32_t*);
            *funcs = I2C_FUNC_I2C | I2C_FUNC_SMBUS_QUICK;
            return 0;
        }
        case I2C_RDWR: {
            struct i2c_rdwr_ioctl_data *data = va_arg(args, struct i2c_rdwr_ioctl_data*);
            // 实现模拟数据传输
            return mock_i2c_transfer(data);
        }
        case I2C_SMBUS: {
            struct i2c_smbus_ioctl_data *args_data = va_arg(args, struct i2c_smbus_ioctl_data*);
            return mock_smbus_transfer(args_data);
        }
        default:
            return -1; // 模拟失败
    }
    
    va_end(args);
    return 0;
}

//测试用例1. 测试I2C设备打开关闭
void test_bI2cDevOpenClose(void) 
{
    // 正常打开
    bI2cDev_t* dev = bI2cOpen(MOCK_DEV_PATH, 0x20);
    TEST_ASSERT_NOT_NULL(dev);
    TEST_ASSERT_EQUAL(100, bI2cGetFd(dev));
    TEST_ASSERT_EQUAL_STRING(MOCK_DEV_PATH, bI2cGetDevPath(dev));
    TEST_ASSERT_EQUAL(0x20, bI2cGetDevAddr(dev));
    
    // 无效路径
    TEST_ASSERT_NULL(bI2cOpen(NULL, 0x20));
    
    // 关闭设备
    bI2cClose(dev);
}

//测试用例2. 测试I2C设备基本操作
void test_bI2cBasicOperations(void) 
{
    bI2cDev_t* dev = bI2cOpen(MOCK_DEV_PATH, 0x20);
    
    // 写测试
    uint8_t write_data[] = {0xAA, 0xBB, 0xCC};
    TEST_ASSERT_EQUAL(I2C_SUCCESS, bI2cWrite(dev, write_data, sizeof(write_data)));
    
    // 读测试
    uint8_t read_buf[3] = {0};
    TEST_ASSERT_EQUAL(I2C_SUCCESS, bI2cRead(dev, read_buf, sizeof(read_buf)));
    TEST_ASSERT_EQUAL_HEX8_ARRAY(write_data, read_buf, sizeof(write_data));
    
    // 复合操作
    uint8_t wdata[] = {0x01, 0x02};
    uint8_t rdata[2] = {0};
    TEST_ASSERT_EQUAL(I2C_SUCCESS, 
        bI2cWriteRead(dev, wdata, sizeof(wdata), rdata, sizeof(rdata)));
    
    bI2cClose(dev);
}

//测试用例3. 测试SMBus操作
void test_bI2cSmbusOperations(void) 
{
    bI2cDev_t* dev = bI2cOpen(MOCK_DEV_PATH, 0x20);
    
    // 快速命令
    TEST_ASSERT_EQUAL(I2C_SUCCESS, bI2cSmbusQuick(dev));
    
    // 字节读写
    TEST_ASSERT_EQUAL(I2C_SUCCESS, bI2cSmbusWriteByte(dev, 0x55));
    uint8_t byte_val = 0;
    TEST_ASSERT_EQUAL(I2C_SUCCESS, bI2cSmbusReadByte(dev, &byte_val));
    TEST_ASSERT_EQUAL(0x55, byte_val);
    
    // // 寄存器读写
    TEST_ASSERT_EQUAL(I2C_SUCCESS, bI2cSmbusWriteByteData(dev, 0x10, 0xAA));
    uint8_t reg_val = 0;
    TEST_ASSERT_EQUAL(I2C_SUCCESS, bI2cSmbusReadByteData(dev, 0x10, &reg_val));
    TEST_ASSERT_EQUAL(0xAA, reg_val);
    
    // // 块数据测试
    uint8_t block_data[] = {0x01, 0x02, 0x03};
    TEST_ASSERT_EQUAL(I2C_SUCCESS, 
        bI2cSmbusWriteBlockData(dev, 0x20, block_data, sizeof(block_data)));
    
    uint8_t read_block[3] = {0};
    uint8_t read_len = sizeof(read_block);
    TEST_ASSERT_EQUAL(I2C_SUCCESS, 
        bI2cSmbusReadBlockData(dev, 0x20, read_block, &read_len));
    TEST_ASSERT_EQUAL(sizeof(block_data), read_len);
    TEST_ASSERT_EQUAL_HEX8_ARRAY(block_data, read_block, read_len);
    
    bI2cClose(dev);
}