/********************************************************************************
* ZHT Communication & technology Co.Ltd 2019-2029 Copyright reserved
* File : mmioTestApi.h
* Function : bMmio test;
* Author : yangzuogui
* Created on: 2025.07.14
*
* Modification history:
* Ver Who Date Changes
* ------------------------------------------------------------------------------
* V1.0 yangzuogui 2025/07/14 First release
********************************************************************************/
#ifndef __MMIO_TEST_API_H__
#define __MMIO_TEST_API_H__
#include "unity.h"
#include "bMmio.h"

void test_bMmioDevCreate(void);
void test_bMmioDevRelease(void);
void test_bMmioDevReadWrite32(void);
void test_bMmioDevReadWrite16(void);
void test_bMmioDevReadWrite8(void);
void test_bMmioDevReadWrite(void);
void test_bMmioDevGetBaseSize(void);

#endif 