/********************************************************************************
* ZHT Communication & technology Co.Ltd 2019-2029 Copyright reserved
* File : mmioTestApi.c
* Function : bMmio test;
* Author : yangzuogui
* Created on: 2025.07.14
*
* Modification history:
* Ver Who Date Changes
* ------------------------------------------------------------------------------
* V1.0 yangzuogui 2025/07/14 First release
********************************************************************************/
#include "mmioTestApi.h"

#define TEST_MMIO_BASE 0x10000000UL
#define TEST_MMIO_SIZE 0x1000UL

void test_bMmioDevCreate(void)
{
    bMmioDev_t *ptMmio = bMmioDevCreate(TEST_MMIO_BASE, TEST_MMIO_SIZE);
    TEST_ASSERT_NOT_NULL(ptMmio);
    bMmioDevRelease(ptMmio);

    ptMmio = bMmioDevCreate(TEST_MMIO_BASE, 0);
    TEST_ASSERT_NULL(ptMmio);
}

void test_bMmioDevRelease(void)
{
    bMmioDev_t *ptMmio = bMmioDevCreate(TEST_MMIO_BASE, TEST_MMIO_SIZE);
    TEST_ASSERT_NOT_NULL(ptMmio);
    int32_t ret = bMmioDevRelease(ptMmio);
    TEST_ASSERT_EQUAL(mmioResult_eSUCCESS, ret);

    ret = bMmioDevRelease(NULL);
    TEST_ASSERT_EQUAL(mmioResult_eERROR_ARG, ret);
}

void test_bMmioDevReadWrite32(void)
{
    bMmioDev_t *ptMmio = bMmioDevCreate(TEST_MMIO_BASE, TEST_MMIO_SIZE);
    TEST_ASSERT_NOT_NULL(ptMmio);
    uint32_t value = 0xA5A5A5A5;
    int ret = bMmioDevWrite32(ptMmio, 0, value);
    TEST_ASSERT_EQUAL(mmioResult_eSUCCESS, ret);
    uint32_t readVal = 0;
    ret = bMmioDevRead32(ptMmio, 0, &readVal);
    TEST_ASSERT_EQUAL(mmioResult_eSUCCESS, ret);
    // 由于物理内存映射，实际读写需硬件支持，这里只做接口调用测试
    bMmioDevRelease(ptMmio);
}

void test_bMmioDevReadWrite16(void)
{
    bMmioDev_t *ptMmio = bMmioDevCreate(TEST_MMIO_BASE, TEST_MMIO_SIZE);
    TEST_ASSERT_NOT_NULL(ptMmio);
    uint16_t value = 0xA5A5;
    int ret = bMmioDevWrite16(ptMmio, 2, value);
    TEST_ASSERT_EQUAL(mmioResult_eSUCCESS, ret);
    uint16_t readVal = 0;
    ret = bMmioDevRead16(ptMmio, 2, &readVal);
    TEST_ASSERT_EQUAL(mmioResult_eSUCCESS, ret);
    bMmioDevRelease(ptMmio);
}

void test_bMmioDevReadWrite8(void)
{
    bMmioDev_t *ptMmio = bMmioDevCreate(TEST_MMIO_BASE, TEST_MMIO_SIZE);
    TEST_ASSERT_NOT_NULL(ptMmio);
    uint8_t value = 0xA5;
    int ret = bMmioDevWrite8(ptMmio, 4, value);
    TEST_ASSERT_EQUAL(mmioResult_eSUCCESS, ret);
    uint8_t readVal = 0;
    ret = bMmioDevRead8(ptMmio, 4, &readVal);
    TEST_ASSERT_EQUAL(mmioResult_eSUCCESS, ret);
    bMmioDevRelease(ptMmio);
}

void test_bMmioDevReadWrite(void)
{
    bMmioDev_t *ptMmio = bMmioDevCreate(TEST_MMIO_BASE, TEST_MMIO_SIZE);
    TEST_ASSERT_NOT_NULL(ptMmio);
    uint8_t buf[8] = {0x11,0x22,0x33,0x44,0x55,0x66,0x77,0x88};
    int ret = bMmioDevWrite(ptMmio, 8, buf, sizeof(buf));
    TEST_ASSERT_EQUAL(mmioResult_eSUCCESS, ret);
    uint8_t readBuf[8] = {0};
    ret = bMmioDevRead(ptMmio, 8, readBuf, sizeof(readBuf));
    TEST_ASSERT_EQUAL(mmioResult_eSUCCESS, ret);
    bMmioDevRelease(ptMmio);
}

void test_bMmioDevGetBaseSize(void)
{
    bMmioDev_t *ptMmio = bMmioDevCreate(TEST_MMIO_BASE, TEST_MMIO_SIZE);
    TEST_ASSERT_NOT_NULL(ptMmio);
    uintptr_t base = bMmioDevGetBase(ptMmio);
    size_t size = bMmioDevGetSize(ptMmio);
    TEST_ASSERT_EQUAL(TEST_MMIO_BASE, base);
    TEST_ASSERT_EQUAL(TEST_MMIO_SIZE, size);
    bMmioDevRelease(ptMmio);
} 