/*******************************************************************************
* ZHT Communication & technology Co.Ltd 2019-2029 Copyright reserved
* File : serialTestApi.h
* Function : bSerial test API declarations
* Author : yangzuogui
* Created on: 2025.07.16
*
* Modification history:
* Ver Who Date Changes
* ------------------------------------------------------------------------------
* V1.0 yangzuogui 2025/07/16 First release
********************************************************************************/
#ifndef __SERIAL_TEST_API_H__
#define __SERIAL_TEST_API_H__

#include <unistd.h>
#include "unity.h"
#include "bSerial.h"

// 基础功能测试
void test_bSerialInit(void);
void test_bSerialRelease(void);
void test_bSerialGetFd(void);

// 读写功能测试
void test_bSerialRead(void);
void test_bSerialWrite(void);
void test_bSerialFlush(void);

// 队列状态测试
void test_bSerialGetInputQueueCnt(void);
void test_bSerialGetOutputQueueCnt(void);
void test_bIsSerialHasData(void);

// 串口参数获取测试
void test_bSerialGetBaudrate(void);
void test_bSerialGetDatabits(void);
void test_bSerialGetParity(void);
void test_bSerialGetStopbits(void);
void test_bSerialGetXonxoff(void);
void test_bSerialGetRtscts(void);
void test_bSerialGetVmin(void);
void test_bSerialGetVtime(void);

// 串口参数设置测试
void test_bSerialSetBaudrate(void);
void test_bSerialSetDatabits(void);
void test_bSerialSetParity(void);
void test_bSerialSetStopbits(void);
void test_bSerialSetXonxoff(void);
void test_bSerialSetRtscts(void);
void test_bSerialSetVmin(void);
void test_bSerialSetVtime(void);

// 错误处理测试
void test_bSerialInitInvalidArgs(void);
void test_bSerialReadWriteInvalidArgs(void);
void test_bSerialSetInvalidParams(void);

#endif 