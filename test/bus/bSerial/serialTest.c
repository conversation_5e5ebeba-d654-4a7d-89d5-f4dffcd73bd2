/*******************************************************************************
* ZHT Communication & technology Co.Ltd 2019-2029 Copyright reserved
* File : serialTest.c
* Function : bSerial test main entry
* Author : yangzuogui
* Created on: 2025.07.16
*
* Modification history:
* Ver Who Date Changes
* ------------------------------------------------------------------------------
* V1.0 yangzuogui 2025/07/16 First release
********************************************************************************/
#include "serialTestApi.h"

int main(void)
{
    UNITY_BEGIN();

    // 基础功能测试
    RUN_TEST(test_bSerialInit);
    RUN_TEST(test_bSerialRelease);
    RUN_TEST(test_bSerialGetFd);

    // 读写功能测试
    RUN_TEST(test_bSerialRead);
    RUN_TEST(test_bSerialWrite);
    RUN_TEST(test_bSerialFlush);

    // 队列状态测试
    RUN_TEST(test_bSerialGetInputQueueCnt);
    RUN_TEST(test_bSerialGetOutputQueueCnt);
    RUN_TEST(test_bIsSerialHasData);

    // 串口参数获取测试
    RUN_TEST(test_bSerialGetBaudrate);
    RUN_TEST(test_bSerialGetDatabits);
    RUN_TEST(test_bSerialGetParity);
    RUN_TEST(test_bSerialGetStopbits);
    RUN_TEST(test_bSerialGetXonxoff);
    RUN_TEST(test_bSerialGetRtscts);
    RUN_TEST(test_bSerialGetVmin);
    RUN_TEST(test_bSerialGetVtime);

    // 串口参数设置测试
    RUN_TEST(test_bSerialSetBaudrate);
    RUN_TEST(test_bSerialSetDatabits);
    RUN_TEST(test_bSerialSetParity);
    RUN_TEST(test_bSerialSetStopbits);
    RUN_TEST(test_bSerialSetXonxoff);
    RUN_TEST(test_bSerialSetRtscts);
    RUN_TEST(test_bSerialSetVmin);
    RUN_TEST(test_bSerialSetVtime);

    // 错误处理测试
    RUN_TEST(test_bSerialInitInvalidArgs);
    RUN_TEST(test_bSerialReadWriteInvalidArgs);
    RUN_TEST(test_bSerialSetInvalidParams);

    UNITY_END();

    return 0;
} 