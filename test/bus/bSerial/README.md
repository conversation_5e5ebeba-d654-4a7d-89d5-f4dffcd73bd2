# bSerial 单元测试

## 概述

本目录包含bSerial库的单元测试代码，使用文件模拟进行测试，完全不需要依赖真实硬件设备。

## 文件结构

- `serialTest.c` - 测试主入口文件
- `serialTestApi.h` - 测试函数声明头文件
- `serialTestApi.c` - 测试函数实现文件（包含文件模拟器）
- `README.md` - 本说明文档

## 文件模拟测试方法

测试使用基于临时文件的模拟方法，具有以下特点：

- **完全虚拟化**：使用临时文件模拟串口设备
- **无需硬件**：不需要真实的串口设备或USB转串口适配器
- **参数验证**：支持所有串口参数的设置和获取测试
- **错误处理**：支持各种错误情况的测试
- **读写测试**：支持基本的读写功能测试

### 工作原理

1. 创建临时文件（`/tmp/test_serial.tmp`）作为模拟串口设备
2. 通过临时文件路径初始化bSerial对象
3. 测试各种API调用，验证参数验证和错误处理
4. 对于读写测试，在文件中预先写入数据或验证写入结果
5. 测试完成后自动清理临时文件

### 测试策略

- **参数验证测试**：测试无效参数的处理
- **错误处理测试**：测试各种错误情况的处理
- **基本功能测试**：测试API调用的基本功能
- **资源管理测试**：测试内存分配和释放

## 测试覆盖范围

### 基础功能测试
- `test_bSerialInit()` - 测试串口初始化（使用文件模拟）
- `test_bSerialRelease()` - 测试串口资源释放
- `test_bSerialGetFd()` - 测试获取文件描述符

### 读写功能测试
- `test_bSerialRead()` - 测试串口读取功能（文件数据源）
- `test_bSerialWrite()` - 测试串口写入功能（文件数据接收）
- `test_bSerialFlush()` - 测试串口缓冲区刷新

### 队列状态测试
- `test_bSerialGetInputQueueCnt()` - 测试获取输入队列计数
- `test_bSerialGetOutputQueueCnt()` - 测试获取输出队列计数
- `test_bIsSerialHasData()` - 测试检查是否有数据可读

### 串口参数获取测试
- `test_bSerialGetBaudrate()` - 测试获取波特率
- `test_bSerialGetDatabits()` - 测试获取数据位
- `test_bSerialGetParity()` - 测试获取校验位
- `test_bSerialGetStopbits()` - 测试获取停止位
- `test_bSerialGetXonxoff()` - 测试获取软件流控制状态
- `test_bSerialGetRtscts()` - 测试获取硬件流控制状态
- `test_bSerialGetVmin()` - 测试获取VMIN值
- `test_bSerialGetVtime()` - 测试获取VTIME值

### 串口参数设置测试
- `test_bSerialSetBaudrate()` - 测试设置波特率
- `test_bSerialSetDatabits()` - 测试设置数据位
- `test_bSerialSetParity()` - 测试设置校验位
- `test_bSerialSetStopbits()` - 测试设置停止位
- `test_bSerialSetXonxoff()` - 测试设置软件流控制
- `test_bSerialSetRtscts()` - 测试设置硬件流控制
- `test_bSerialSetVmin()` - 测试设置VMIN值
- `test_bSerialSetVtime()` - 测试设置VTIME值

### 错误处理测试
- `test_bSerialInitInvalidArgs()` - 测试无效参数初始化
- `test_bSerialReadWriteInvalidArgs()` - 测试无效读写参数
- `test_bSerialSetInvalidParams()` - 测试无效参数设置

## 编译和运行

### 编译
```bash
cd test
mkdir build
cd build
cmake ..
make
```

### 运行测试
```bash
./test-bus-bSerial
```

## 测试环境要求

1. **Linux系统** - 测试基于Linux系统设计
2. **文件系统支持** - 需要支持临时文件创建
3. **标准C库** - 需要支持基本的文件操作

## 优势

1. **无硬件依赖** - 不需要真实的串口设备
2. **可重复性** - 测试结果稳定，不受硬件状态影响
3. **快速执行** - 无需等待硬件响应
4. **完整覆盖** - 可以测试所有API功能和错误处理
5. **CI/CD友好** - 适合自动化测试环境
6. **参数验证** - 重点测试参数验证和错误处理逻辑

## 测试结果说明

- **成功** - 所有测试通过，串口功能正常
- **失败** - 发现功能缺陷或实现错误

## 测试重点

本测试主要关注以下方面：

1. **参数验证** - 确保无效参数被正确检测和处理
2. **错误处理** - 确保各种错误情况被正确处理
3. **资源管理** - 确保内存分配和释放正确
4. **API完整性** - 确保所有API函数可以正常调用
5. **边界条件** - 测试各种边界条件和异常情况

## 扩展测试

如需添加更多测试用例，请：

1. 在 `serialTestApi.h` 中声明新的测试函数
2. 在 `serialTestApi.c` 中实现测试逻辑（使用文件模拟环境）
3. 在 `serialTest.c` 中添加测试调用
4. 更新本README文档

## 注意事项

- 文件模拟测试主要验证API的参数验证和错误处理逻辑
- 由于使用文件而非真实串口，某些termios操作可能返回错误，这是预期的
- 所有测试都会自动清理临时文件，不会造成文件系统污染
- 测试重点在于验证代码逻辑而非硬件功能 