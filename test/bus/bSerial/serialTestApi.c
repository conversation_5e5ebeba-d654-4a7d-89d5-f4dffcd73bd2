#define _GNU_SOURCE
#include "serialTestApi.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <fcntl.h>
#include <unistd.h>
#include <sys/socket.h>
#include <sys/un.h>
#include <errno.h>
#include <pthread.h>

// Temporary file path for testing
#define TEST_TEMP_FILE "/tmp/test_serial.tmp"
#define TEST_INVALID_SERIAL_DEV "/dev/ttyUSB0"

// Create test file
static int createTestFile(void)
{
    int fd = open(TEST_TEMP_FILE, O_RDWR | O_CREAT | O_TRUNC, 0644);
    if (fd < 0) {
        return -1;
    }
    close(fd);
    return 0;
}

// Delete test file
static void cleanupTestFile(void)
{
    unlink(TEST_TEMP_FILE);
}

// Basic function tests
void test_bSerialInit(void)
{
    // Create test file
    if (createTestFile() == 0) {
        bSerialDev_t *serial = bSerialInit(TEST_TEMP_FILE, 9600, 8, bSerialParity_eNONE, 1);
        // Since the file is not a real serial device, termios operations will fail, which is expected
        if (serial != NULL) {
            bSerialRelease(serial);
        }
        cleanupTestFile();
    }
    // Test NULL path
    bSerialDev_t *serial = bSerialInit(NULL, 9600, 8, bSerialParity_eNONE, 1);
    TEST_ASSERT_NULL(serial);
}

void test_bSerialRelease(void)
{
    // Test NULL parameter
    bSerialRelease(NULL);
    
    // Test normal release (if init succeeded)
    if (createTestFile() == 0) {
        bSerialDev_t *serial = bSerialInit(TEST_TEMP_FILE, 9600, 8, bSerialParity_eNONE, 1);
        if (serial != NULL) {
            bSerialRelease(serial);
        }
        cleanupTestFile();
    }
}

void test_bSerialGetFd(void)
{
    if (createTestFile() == 0) {
        bSerialDev_t *serial = bSerialInit(TEST_TEMP_FILE, 9600, 8, bSerialParity_eNONE, 1);
        if (serial != NULL) {
            int fd = bSerialGetFd(serial);
            TEST_ASSERT_GREATER_THAN(-1, fd);
            bSerialRelease(serial);
        }
        cleanupTestFile();
    }
}

// Read/write function tests - file simulation
void test_bSerialRead(void)
{
    if (createTestFile() == 0) {
        // Write some test data to file first
        int writeFd = open(TEST_TEMP_FILE, O_WRONLY);
        if (writeFd >= 0) {
            const char testData[] = "Hello Serial Test Data";
            write(writeFd, testData, strlen(testData));
            close(writeFd);
            
            // Create serial object
            bSerialDev_t *serial = bSerialInit(TEST_TEMP_FILE, 9600, 8, bSerialParity_eNONE, 1);
            if (serial != NULL) {
                uint8_t readBuf[256] = {0};
                int readLen = bSerialRead(serial, readBuf, sizeof(readBuf), 1000);
                
                // Since the file is not a real serial port, read may fail, which is expected
                // The main purpose is to test that the function call does not crash
                TEST_ASSERT_LESS_OR_EQUAL((int)strlen(testData), readLen);
                
                bSerialRelease(serial);
            }
        }
        cleanupTestFile();
    }
}

void test_bSerialWrite(void)
{
    if (createTestFile() == 0) {
        bSerialDev_t *serial = bSerialInit(TEST_TEMP_FILE, 9600, 8, bSerialParity_eNONE, 1);
        if (serial != NULL) {
            const char testData[] = "Hello Serial Write Test";
            int writeLen = bSerialWrite(serial, (uint8_t*)testData, strlen(testData));
            
            // Write should succeed
            TEST_ASSERT_EQUAL(strlen(testData), writeLen);
            
            bSerialRelease(serial);
        }
        cleanupTestFile();
    }
}

void test_bSerialFlush(void)
{
    if (createTestFile() == 0) {
        bSerialDev_t *serial = bSerialInit(TEST_TEMP_FILE, 9600, 8, bSerialParity_eNONE, 1);
        if (serial != NULL) {
            int ret = bSerialFlush(serial);
            // flush should return success or appropriate error code
            TEST_ASSERT_LESS_OR_EQUAL(0, ret);
            bSerialRelease(serial);
        }
        cleanupTestFile();
    }
}

// Queue status tests
void test_bSerialGetInputQueueCnt(void)
{
    if (createTestFile() == 0) {
        bSerialDev_t *serial = bSerialInit(TEST_TEMP_FILE, 9600, 8, bSerialParity_eNONE, 1);
        if (serial != NULL) {
            int32_t count = bSerialGetInputQueueCnt(serial);
            // 应返回>=0的字节数或负数错误码
            TEST_ASSERT_LESS_OR_EQUAL(0, count);
            bSerialRelease(serial);
        }
        cleanupTestFile();
    }
}

void test_bSerialGetOutputQueueCnt(void)
{
    if (createTestFile() == 0) {
        bSerialDev_t *serial = bSerialInit(TEST_TEMP_FILE, 9600, 8, bSerialParity_eNONE, 1);
        if (serial != NULL) {
            int32_t count = bSerialGetOutputQueueCnt(serial);
            // 应返回>=0的字节数或负数错误码
            TEST_ASSERT_LESS_OR_EQUAL(0, count);
            bSerialRelease(serial);
        }
        cleanupTestFile();
    }
}

void test_bIsSerialHasData(void)
{
    if (createTestFile() == 0) {
        bSerialDev_t *serial = bSerialInit(TEST_TEMP_FILE, 9600, 8, bSerialParity_eNONE, 1);
        if (serial != NULL) {
            int ret = bIsSerialHasData(serial, 100);
            // Should return 0 (timeout) or appropriate error code
            TEST_ASSERT_LESS_OR_EQUAL(0, ret);
            bSerialRelease(serial);
        }
        cleanupTestFile();
    }
}

// Serial parameter get tests
void test_bSerialGetBaudrate(void)
{
    if (createTestFile() == 0) {
        bSerialDev_t *serial = bSerialInit(TEST_TEMP_FILE, 9600, 8, bSerialParity_eNONE, 1);
        if (serial != NULL) {
            uint32_t baudrate;
            int ret = bSerialGetBaudrate(serial, &baudrate);
            // Since this is not a real serial device, may return error, which is expected
            TEST_ASSERT_LESS_OR_EQUAL(0, ret);
            bSerialRelease(serial);
        }
        cleanupTestFile();
    }
}

void test_bSerialGetDatabits(void)
{
    if (createTestFile() == 0) {
        bSerialDev_t *serial = bSerialInit(TEST_TEMP_FILE, 9600, 8, bSerialParity_eNONE, 1);
        if (serial != NULL) {
            unsigned int databits;
            int ret = bSerialGetDatabits(serial, &databits);
            TEST_ASSERT_LESS_OR_EQUAL(0, ret);
            bSerialRelease(serial);
        }
        cleanupTestFile();
    }
}

void test_bSerialGetParity(void)
{
    if (createTestFile() == 0) {
        bSerialDev_t *serial = bSerialInit(TEST_TEMP_FILE, 9600, 8, bSerialParity_eNONE, 1);
        if (serial != NULL) {
            bSerialParity_e parity;
            int ret = bSerialGetParity(serial, &parity);
            TEST_ASSERT_LESS_OR_EQUAL(0, ret);
            bSerialRelease(serial);
        }
        cleanupTestFile();
    }
}

void test_bSerialGetStopbits(void)
{
    if (createTestFile() == 0) {
        bSerialDev_t *serial = bSerialInit(TEST_TEMP_FILE, 9600, 8, bSerialParity_eNONE, 1);
        if (serial != NULL) {
            unsigned int stopbits;
            int ret = bSerialGetStopbits(serial, &stopbits);
            TEST_ASSERT_LESS_OR_EQUAL(0, ret);
            bSerialRelease(serial);
        }
        cleanupTestFile();
    }
}

void test_bSerialGetXonxoff(void)
{
    if (createTestFile() == 0) {
        bSerialDev_t *serial = bSerialInit(TEST_TEMP_FILE, 9600, 8, bSerialParity_eNONE, 1);
        if (serial != NULL) {
            bool xonxoff;
            int ret = bSerialGetXonxoff(serial, &xonxoff);
            TEST_ASSERT_LESS_OR_EQUAL(0, ret);
            bSerialRelease(serial);
        }
        cleanupTestFile();
    }
}

void test_bSerialGetRtscts(void)
{
    if (createTestFile() == 0) {
        bSerialDev_t *serial = bSerialInit(TEST_TEMP_FILE, 9600, 8, bSerialParity_eNONE, 1);
        if (serial != NULL) {
            bool rtscts;
            int ret = bSerialGetRtscts(serial, &rtscts);
            TEST_ASSERT_LESS_OR_EQUAL(0, ret);
            bSerialRelease(serial);
        }
        cleanupTestFile();
    }
}

void test_bSerialGetVmin(void)
{
    if (createTestFile() == 0) {
        bSerialDev_t *serial = bSerialInit(TEST_TEMP_FILE, 9600, 8, bSerialParity_eNONE, 1);
        if (serial != NULL) {
            unsigned int vmin;
            int ret = bSerialGetVmin(serial, &vmin);
            TEST_ASSERT_LESS_OR_EQUAL(0, ret);
            bSerialRelease(serial);
        }
        cleanupTestFile();
    }
}

void test_bSerialGetVtime(void)
{
    if (createTestFile() == 0) {
        bSerialDev_t *serial = bSerialInit(TEST_TEMP_FILE, 9600, 8, bSerialParity_eNONE, 1);
        if (serial != NULL) {
            uint32_t vtimeMs;
            int ret = bSerialGetVtime(serial, &vtimeMs);
            TEST_ASSERT_LESS_OR_EQUAL(0, ret);
            bSerialRelease(serial);
        }
        cleanupTestFile();
    }
}

// Serial parameter set tests
void test_bSerialSetBaudrate(void)
{
    if (createTestFile() == 0) {
        bSerialDev_t *serial = bSerialInit(TEST_TEMP_FILE, 9600, 8, bSerialParity_eNONE, 1);
        if (serial != NULL) {
            int ret = bSerialSetBaudrate(serial, 115200);
            TEST_ASSERT_LESS_OR_EQUAL(0, ret);
            bSerialRelease(serial);
        }
        cleanupTestFile();
    }
}

void test_bSerialSetDatabits(void)
{
    if (createTestFile() == 0) {
        bSerialDev_t *serial = bSerialInit(TEST_TEMP_FILE, 9600, 8, bSerialParity_eNONE, 1);
        if (serial != NULL) {
            int ret = bSerialSetDatabits(serial, 7);
            TEST_ASSERT_LESS_OR_EQUAL(0, ret);
            bSerialRelease(serial);
        }
        cleanupTestFile();
    }
}

void test_bSerialSetParity(void)
{
    if (createTestFile() == 0) {
        bSerialDev_t *serial = bSerialInit(TEST_TEMP_FILE, 9600, 8, bSerialParity_eNONE, 1);
        if (serial != NULL) {
            int ret = bSerialSetParity(serial, bSerialParity_eEVEN);
            TEST_ASSERT_LESS_OR_EQUAL(0, ret);
            bSerialRelease(serial);
        }
        cleanupTestFile();
    }
}

void test_bSerialSetStopbits(void)
{
    if (createTestFile() == 0) {
        bSerialDev_t *serial = bSerialInit(TEST_TEMP_FILE, 9600, 8, bSerialParity_eNONE, 1);
        if (serial != NULL) {
            int ret = bSerialSetStopbits(serial, 2);
            TEST_ASSERT_LESS_OR_EQUAL(0, ret);
            bSerialRelease(serial);
        }
        cleanupTestFile();
    }
}

void test_bSerialSetXonxoff(void)
{
    if (createTestFile() == 0) {
        bSerialDev_t *serial = bSerialInit(TEST_TEMP_FILE, 9600, 8, bSerialParity_eNONE, 1);
        if (serial != NULL) {
            int ret = bSerialSetXonxoff(serial, true);
            TEST_ASSERT_LESS_OR_EQUAL(0, ret);
            bSerialRelease(serial);
        }
        cleanupTestFile();
    }
}

void test_bSerialSetRtscts(void)
{
    if (createTestFile() == 0) {
        bSerialDev_t *serial = bSerialInit(TEST_TEMP_FILE, 9600, 8, bSerialParity_eNONE, 1);
        if (serial != NULL) {
            int ret = bSerialSetRtscts(serial, true);
            TEST_ASSERT_LESS_OR_EQUAL(0, ret);
            bSerialRelease(serial);
        }
        cleanupTestFile();
    }
}

void test_bSerialSetVmin(void)
{
    if (createTestFile() == 0) {
        bSerialDev_t *serial = bSerialInit(TEST_TEMP_FILE, 9600, 8, bSerialParity_eNONE, 1);
        if (serial != NULL) {
            int ret = bSerialSetVmin(serial, 10);
            TEST_ASSERT_LESS_OR_EQUAL(0, ret);
            bSerialRelease(serial);
        }
        cleanupTestFile();
    }
}

void test_bSerialSetVtime(void)
{
    if (createTestFile() == 0) {
        bSerialDev_t *serial = bSerialInit(TEST_TEMP_FILE, 9600, 8, bSerialParity_eNONE, 1);
        if (serial != NULL) {
            int ret = bSerialSetVtime(serial, 1500); // 1.5秒
            TEST_ASSERT_LESS_OR_EQUAL(0, ret);
            bSerialRelease(serial);
        }
        cleanupTestFile();
    }
}

// Error handling tests
void test_bSerialInitInvalidArgs(void)
{
    // Test NULL path
    bSerialDev_t *serial = bSerialInit(NULL, 9600, 8, bSerialParity_eNONE, 1);
    TEST_ASSERT_NULL(serial);
    
    // Test invalid data bits
    serial = bSerialInit(TEST_INVALID_SERIAL_DEV, 9600, 9, bSerialParity_eNONE, 1);
    TEST_ASSERT_NULL(serial);
    
    // Test invalid stop bits
    serial = bSerialInit(TEST_INVALID_SERIAL_DEV, 9600, 8, bSerialParity_eNONE, 3);
    TEST_ASSERT_NULL(serial);
}

void test_bSerialReadWriteInvalidArgs(void)
{
    if (createTestFile() == 0) {
        bSerialDev_t *serial = bSerialInit(TEST_TEMP_FILE, 9600, 8, bSerialParity_eNONE, 1);
        if (serial != NULL) {
            // Test NULL buffer
            int ret = bSerialRead(serial, NULL, 10, 1000);
            TEST_ASSERT_LESS_THAN(0, ret);
            
            ret = bSerialWrite(serial, NULL, 10);
            TEST_ASSERT_LESS_THAN(0, ret);
            
            bSerialRelease(serial);
        }
        cleanupTestFile();
    }
}

void test_bSerialSetInvalidParams(void)
{
    if (createTestFile() == 0) {
        bSerialDev_t *serial = bSerialInit(TEST_TEMP_FILE, 9600, 8, bSerialParity_eNONE, 1);
        if (serial != NULL) {
            // Test invalid data bits
            int ret = bSerialSetDatabits(serial, 9);
            TEST_ASSERT_EQUAL(bSerialResult_eERROR_ARG, ret);
            
            // Test invalid stop bits
            ret = bSerialSetStopbits(serial, 3);
            TEST_ASSERT_EQUAL(bSerialResult_eERROR_ARG, ret);
            
            // Test invalid VMIN value
            ret = bSerialSetVmin(serial, 256);
            TEST_ASSERT_EQUAL(bSerialResult_eERROR_ARG, ret);
            
            // Test invalid VTIME value
            ret = bSerialSetVtime(serial, 26000); // 超过25500ms
            TEST_ASSERT_EQUAL(bSerialResult_eERROR_ARG, ret);
            
            bSerialRelease(serial);
        }
        cleanupTestFile();
    }
} 