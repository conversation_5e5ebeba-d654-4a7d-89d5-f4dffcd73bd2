/*******************************************************************************
* ZHT Communication & technology Co.Ltd 2019-2029 Copyright reserved
* File : gpioTestApi.c
* Function : bGpio test;
* Author : yangzuogui
* Created on: 2025.07.11
*
* Modification history:
* Ver Who Date Changes
* ------------------------------------------------------------------------------
* V1.0 yangzuogui 2025/07/11 First release
********************************************************************************/
#include "gpioTestApi.h"

void test_bGpioExport(void)
{
    gpioResult_e ret;

    // Test invalid GPIO pin (out of range)
    ret = bGpioExport(GPIO_PIN_MAX + 1);
    TEST_ASSERT_EQUAL(gpioResult_eERROR_INVALID_PIN, ret);

    // Test boundary values
    ret = bGpioExport(0);
    // Note: This may fail on systems without GPIO support, which is expected
    // We don't assert on the result since it depends on hardware

    ret = bGpioExport(GPIO_PIN_MAX);
    // Note: This may fail on systems without GPIO support, which is expected
    // We don't assert on the result since it depends on hardware
}

void test_bGpioUnexport(void)
{
    gpioResult_e ret;

    // Test invalid GPIO pin (out of range)
    ret = bGpioUnexport(GPIO_PIN_MAX + 1);
    TEST_ASSERT_EQUAL(gpioResult_eERROR_INVALID_PIN, ret);

    // Test boundary values
    ret = bGpioUnexport(0);
    // Note: This may fail on systems without GPIO support, which is expected
    // We don't assert on the result since it depends on hardware

    ret = bGpioUnexport(GPIO_PIN_MAX);
    // Note: This may fail on systems without GPIO support, which is expected
    // We don't assert on the result since it depends on hardware
}

void test_bGpioSetInput(void)
{
    gpioResult_e ret;

    // Test invalid GPIO pin (out of range)
    ret = bGpioSetInput(GPIO_PIN_MAX + 1);
    TEST_ASSERT_EQUAL(gpioResult_eERROR_INVALID_PIN, ret);

    // Test boundary values
    ret = bGpioSetInput(0);
    // Note: This may fail on systems without GPIO support, which is expected
    // We don't assert on the result since it depends on hardware

    ret = bGpioSetInput(GPIO_PIN_MAX);
    // Note: This may fail on systems without GPIO support, which is expected
    // We don't assert on the result since it depends on hardware
}

void test_bGpioSetEdge(void)
{
    gpioResult_e ret;

    // Test invalid GPIO pin (out of range)
    ret = bGpioSetEdge(GPIO_PIN_MAX + 1, gpioEdge_eNONE);
    TEST_ASSERT_EQUAL(gpioResult_eERROR_INVALID_PIN, ret);

    // Test invalid edge type (assuming 4 is invalid)
    ret = bGpioSetEdge(8, 4);
    TEST_ASSERT_EQUAL(gpioResult_eERROR_INVALID_PARAM, ret);

    // Test valid edge types with valid GPIO pin
    ret = bGpioSetEdge(0, gpioEdge_eNONE);
    // Note: This may fail on systems without GPIO support, which is expected
    // We don't assert on the result since it depends on hardware

    ret = bGpioSetEdge(0, gpioEdge_eRISING);
    // Note: This may fail on systems without GPIO support, which is expected
    // We don't assert on the result since it depends on hardware

    ret = bGpioSetEdge(0, gpioEdge_eFALLING);
    // Note: This may fail on systems without GPIO support, which is expected
    // We don't assert on the result since it depends on hardware

    ret = bGpioSetEdge(0, gpioEdge_eBOTH);
    // Note: This may fail on systems without GPIO support, which is expected
    // We don't assert on the result since it depends on hardware
}

void test_bGpioSetOutput(void)
{
    gpioResult_e ret;

    // Test invalid GPIO pin (out of range)
    ret = bGpioSetOutput(GPIO_PIN_MAX + 1, gpioLevel_eLOW);
    TEST_ASSERT_EQUAL(gpioResult_eERROR_INVALID_PIN, ret);

    // Test invalid level (assuming 2 is invalid)
    ret = bGpioSetOutput(11, 2);
    TEST_ASSERT_EQUAL(gpioResult_eERROR_INVALID_PARAM, ret);

    // Test valid output levels with valid GPIO pin
    ret = bGpioSetOutput(0, gpioLevel_eLOW);
    // Note: This may fail on systems without GPIO support, which is expected
    // We don't assert on the result since it depends on hardware

    ret = bGpioSetOutput(0, gpioLevel_eHIGH);
    // Note: This may fail on systems without GPIO support, which is expected
    // We don't assert on the result since it depends on hardware
}

void test_bGpioGetValue(void)
{
    gpioResult_e ret;
    gpioLevel_e level;

    // Test invalid GPIO pin (out of range)
    ret = bGpioGetValue(GPIO_PIN_MAX + 1, &level);
    TEST_ASSERT_EQUAL(gpioResult_eERROR_INVALID_PIN, ret);

    // Test null pointer
    ret = bGpioGetValue(0, NULL);
    TEST_ASSERT_EQUAL(gpioResult_eERROR_NULL_PTR, ret);

    // Test valid GPIO pin
    ret = bGpioGetValue(0, &level);
    // Note: This may fail on systems without GPIO support, which is expected
    // We don't assert on the result since it depends on hardware
}

void test_bGpioSetDirOutput(void)
{
    gpioResult_e ret;

    // Test invalid GPIO pin (out of range)
    ret = bGpioSetDirOutput(GPIO_PIN_MAX + 1, gpioLevel_eLOW);
    TEST_ASSERT_EQUAL(gpioResult_eERROR_INVALID_PIN, ret);

    // Test invalid level (assuming 2 is invalid)
    ret = bGpioSetDirOutput(15, 2);
    TEST_ASSERT_EQUAL(gpioResult_eERROR_INVALID_PARAM, ret);

    // Test valid output levels with valid GPIO pin
    ret = bGpioSetDirOutput(0, gpioLevel_eLOW);
    // Note: This may fail on systems without GPIO support, which is expected
    // We don't assert on the result since it depends on hardware

    ret = bGpioSetDirOutput(0, gpioLevel_eHIGH);
    // Note: This may fail on systems without GPIO support, which is expected
    // We don't assert on the result since it depends on hardware
}

void test_bGpioGetDirection(void)
{
    gpioResult_e ret;
    gpioDir_e direction;

    // Test invalid GPIO pin (out of range)
    ret = bGpioGetDirection(GPIO_PIN_MAX + 1, &direction);
    TEST_ASSERT_EQUAL(gpioResult_eERROR_INVALID_PIN, ret);

    // Test null pointer
    ret = bGpioGetDirection(0, NULL);
    TEST_ASSERT_EQUAL(gpioResult_eERROR_NULL_PTR, ret);

    // Test valid GPIO pin
    ret = bGpioGetDirection(0, &direction);
    // Note: This may fail on systems without GPIO support, which is expected
    // We don't assert on the result since it depends on hardware
} 