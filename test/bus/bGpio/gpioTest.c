/*
 * *****************************************************************************************
 * ZHT Communication & technology Co.Ltd 2019-2029 Copyright Reserved.
 * @FilePath: /BSL/test/bus/bGpio/gpioTest.c
 * @Description: 
 * @Author: yangzuogui <EMAIL>
 * @Date: 2025-07-11 17:13:58
 * @LastEditors: yangzuogui <EMAIL>
 * @LastEditTime: 2025-07-14 14:06:47
 * *****************************************************************************************
 */
#include "gpioTestApi.h"

int main() 
{
    UNITY_BEGIN();

    RUN_TEST(test_bGpioExport);
    RUN_TEST(test_bGpioUnexport);
    RUN_TEST(test_bGpioSetInput);
    RUN_TEST(test_bGpioSetEdge);
    RUN_TEST(test_bGpioSetOutput);
    RUN_TEST(test_bGpioGetValue);
    RUN_TEST(test_bGpioSetDirOutput);
    RUN_TEST(test_bGpioGetDirection);

    UNITY_END();
    return 0;
} 