/*******************************************************************************
* ZHT Communication & technology Co.Ltd 2019-2029 Copyright reserved
* File : gpioTestApi.h
* Function : bGpio test;
* Author : yangzuogui
* Created on: 2025.07.11
*
* Modification history:
* Ver Who Date Changes
* ------------------------------------------------------------------------------
* V1.0 yangzuogui 2025/07/11 First release
********************************************************************************/
#ifndef __GPIO_TEST_API_H__
#define __GPIO_TEST_API_H__
#include <unistd.h>
#include "unity.h"
#include "bGpio.h"

void test_bGpioExport(void);

void test_bGpioUnexport(void);

void test_bGpioSetInput(void);

void test_bGpioSetEdge(void);

void test_bGpioSetOutput(void);

void test_bGpioGetValue(void);

void test_bGpioSetDirOutput(void);

void test_bGpioGetDirection(void);

#endif 