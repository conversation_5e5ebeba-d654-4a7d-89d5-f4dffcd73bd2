
cmake_minimum_required(VERSION 3.0)

project(BSL-TEST)

set(TEST_SON_DIRS "${PROJECT_SOURCE_DIR}")

file(GLOB CHILD_DIRS RELATIVE "${TEST_SON_DIRS}" "${TEST_SON_DIRS}/*")

foreach(DIR ${CHILD_DIRS})
    if(IS_DIRECTORY "${TEST_SON_DIRS}/${DIR}")
        # 检查该目录下是否有.c和.h文件
        file(GLOB SRC_FILES "${TEST_SON_DIRS}/${DIR}/*.c")
        file(GLOB HDR_FILES "${TEST_SON_DIRS}/${DIR}/*.h")
        if(SRC_FILES AND HDR_FILES)
            set(TARGET_NAME test-${DIR})
            set(SRC_LIST "")

            aux_source_directory(${TEST_SON_DIRS}/${DIR} SRC_LIST)
            aux_source_directory(${PROJECT_SOURCE_DIR}/../3rdpartyLibs/unity/src SRC_LIST)

            include_directories(${PROJECT_SOURCE_DIR}/../3rdpartyLibs/unity/src)
            include_directories(${PROJECT_SOURCE_DIR}/../target/install/include)

            add_executable(${TARGET_NAME} ${SRC_LIST})

            target_link_libraries(${TARGET_NAME} ${PROJECT_SOURCE_DIR}/../target/install/lib/libbsl.a)
            target_link_libraries(${TARGET_NAME} ${PROJECT_SOURCE_DIR}/../target/install/lib/libcjson.a)
            target_link_libraries(${TARGET_NAME} ${PROJECT_SOURCE_DIR}/../target/install/lib/libzlog.a)

            target_link_libraries(${TARGET_NAME} pthread.so)

            install(TARGETS ${TARGET_NAME}
                LIBRARY DESTINATION ${PROJECT_SOURCE_DIR}/../target/install/lib
                ARCHIVE DESTINATION ${PROJECT_SOURCE_DIR}/../target/install/lib
                RUNTIME DESTINATION ${PROJECT_SOURCE_DIR}/../target/install/bin
            )
        endif()
        # 递归处理子目录
        file(GLOB SUBDIRS RELATIVE "${TEST_SON_DIRS}/${DIR}" "${TEST_SON_DIRS}/${DIR}/*")
        foreach(SUBDIR ${SUBDIRS})
            if(IS_DIRECTORY "${TEST_SON_DIRS}/${DIR}/${SUBDIR}")
                file(GLOB SUB_SRC_FILES "${TEST_SON_DIRS}/${DIR}/${SUBDIR}/*.c")
                file(GLOB SUB_HDR_FILES "${TEST_SON_DIRS}/${DIR}/${SUBDIR}/*.h")
                if(SUB_SRC_FILES AND SUB_HDR_FILES)
                    set(TARGET_NAME test-${DIR}-${SUBDIR})
                    set(SRC_LIST "")

                    aux_source_directory(${TEST_SON_DIRS}/${DIR}/${SUBDIR} SRC_LIST)
                    aux_source_directory(${PROJECT_SOURCE_DIR}/../3rdpartyLibs/unity/src SRC_LIST)

                    include_directories(${PROJECT_SOURCE_DIR}/../3rdpartyLibs/unity/src)
                    include_directories(${PROJECT_SOURCE_DIR}/../target/install/include)

                    add_executable(${TARGET_NAME} ${SRC_LIST})

                    target_link_libraries(${TARGET_NAME} ${PROJECT_SOURCE_DIR}/../target/install/lib/libbsl.a)
                    target_link_libraries(${TARGET_NAME} ${PROJECT_SOURCE_DIR}/../target/install/lib/libcjson.a)
                    target_link_libraries(${TARGET_NAME} ${PROJECT_SOURCE_DIR}/../target/install/lib/libzlog.a)

                    target_link_libraries(${TARGET_NAME} pthread.so)


                    if(TARGET_NAME STREQUAL "test-bus-bI2c" OR TARGET_NAME STREQUAL "test-bus-bSpi")
                        set_property(TARGET ${TARGET_NAME} APPEND_STRING PROPERTY 
                            LINK_FLAGS " -Wl,--wrap=open -Wl,--wrap=close -Wl,--wrap=ioctl"
                        )
                    endif()

                    install(TARGETS ${TARGET_NAME}
                        LIBRARY DESTINATION ${PROJECT_SOURCE_DIR}/../target/install/lib
                        ARCHIVE DESTINATION ${PROJECT_SOURCE_DIR}/../target/install/lib
                        RUNTIME DESTINATION ${PROJECT_SOURCE_DIR}/../target/install/bin
                    )
                endif()
            endif()
        endforeach()
    endif()
endforeach()
