#!/bin/sh

ROOT_DIR=$(cd "$(dirname $0)"; pwd)

GCC_TOOLCHAIN_FILE=gcc-toolchain.conf
if [ ! -f $GCC_TOOLCHAIN_FILE ]; then
    echo -n "GCC_TOOLCHAIN=gcc" > $GCC_TOOLCHAIN_FILE
fi

# Read gcc tool chain
COMPILE_TOOL=`cat $GCC_TOOLCHAIN_FILE | awk -F= '{print $2}'`
echo -e COMPILE_TOOL=\"$COMPILE_TOOL\"

TARGET_DIR=$ROOT_DIR/target

# Build directory
BUILD_DIR=$TARGET_DIR/build
rm -rf $BUILD_DIR
[ ! -d $BUILD_DIR ] && mkdir -p $BUILD_DIR

# Install directory
INSTALL_DIR=$TARGET_DIR/install
rm -rf $INSTALL_DIR
INSTALL_DIR_BIN=$INSTALL_DIR/bin
INSTALL_DIR_LIB=$INSTALL_DIR/lib
INSTALL_DIR_INC=$INSTALL_DIR/include
[ ! -d $INSTALL_DIR_BIN ] && mkdir -p $INSTALL_DIR_BIN
[ ! -d $INSTALL_DIR_LIB ] && mkdir -p $INSTALL_DIR_LIB
[ ! -d $INSTALL_DIR_INC ] && mkdir -p $INSTALL_DIR_INC


# Compile zlog
function compile_zlog()
{
    ZLOG_SRC_DIR=$ROOT_DIR/3rdpartyLibs/zlog/src/zlog-1.2.18

    cd $ZLOG_SRC_DIR

    make PREFIX=$INSTALL_DIR CC=$COMPILE_TOOL
    make PREFIX=$INSTALL_DIR install CC=$COMPILE_TOOL
    make clean

    if [ ! -f $INSTALL_DIR_LIB/libzlog.so ]; then
        echo "error, libzlog.so is not exist!"
        exit 99
    fi
}

compile_zlog

# Compile cJSON
function compile_cJSON()
{
    CJSON_SRC_DIR=$ROOT_DIR/3rdpartyLibs/cJSON/src/cJSON-1.7.18
    CJSON_BUILD_DIR=$BUILD_DIR/cJSON
    [ ! -d $CJSON_BUILD_DIR ] && mkdir -p $CJSON_BUILD_DIR

    cd $CJSON_BUILD_DIR
    cmake $CJSON_SRC_DIR -DCMAKE_C_COMPILER=$COMPILE_TOOL -DENABLE_CJSON_UTILS=Off -DENABLE_CJSON_TEST=Off -DBUILD_SHARED_AND_STATIC_LIBS=on
    make

    if [ ! -f $CJSON_BUILD_DIR/libcjson.so ]; then
        echo "error, libcjson.so is not exist!"
        exit 99
    fi
    if [ -f $CJSON_BUILD_DIR/libcjson.a ]; then
        cp -a $CJSON_BUILD_DIR/libcjson.a $INSTALL_DIR_LIB
    fi
    cp -a $CJSON_BUILD_DIR/libcjson.so* $INSTALL_DIR_LIB
    cp $CJSON_SRC_DIR/cJSON.h $INSTALL_DIR_INC
}

compile_cJSON

# compile uthash
function compile_uthash()
{
    UTHASH_SRC_DIR=$ROOT_DIR/3rdpartyLibs/uthash
    cp $UTHASH_SRC_DIR/*.h $INSTALL_DIR_INC
}
compile_uthash

# compile bsl
function compile_bsl()
{
    BSL_SRC_DIR=$ROOT_DIR
    BSL_BUILD_DIR=$BUILD_DIR/BSL
    if [ -d $BSL_BUILD_DIR ]; then
        rm -rf $BSL_BUILD_DIR
    fi
    mkdir -p $BSL_BUILD_DIR

    cd $BSL_BUILD_DIR
    cmake $BSL_SRC_DIR -DCMAKE_C_COMPILER=$COMPILE_TOOL
    make
    make install
    if [[ $? -ne 0 ]]; then
        return 99
    fi
}

compile_bsl
if [[ $? -ne 0 ]]; then
  exit $?
fi

# Compile bsl-test
function compile_bsl_test()
{
    BSL_TEST_SRC_DIR=$ROOT_DIR/test
    BSL_TEST_BUILD_DIR=$BUILD_DIR/BSL-TEST
    if [ -d $BSL_TEST_BUILD_DIR ]; then
        rm -rf $BSL_TEST_BUILD_DIR
    fi
    mkdir -p $BSL_TEST_BUILD_DIR

    cd $BSL_TEST_BUILD_DIR
    cmake $BSL_TEST_SRC_DIR -DCMAKE_C_COMPILER=$COMPILE_TOOL
    make
    make install
    if [[ $? -ne 0 ]]; then
        return 99
    fi
}

compile_bsl_test
if [[ $? -ne 0 ]]; then
  exit $?
fi
